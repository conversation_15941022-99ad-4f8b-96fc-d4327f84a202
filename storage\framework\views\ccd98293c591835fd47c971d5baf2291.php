<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - مساعدك الذكي الخارق</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-ai.css')); ?>">

    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --dark-text: #f1f5f9;
            --dark-text-secondary: #94a3b8;
            --light-bg: #ffffff;
            --light-surface: #f8fafc;
            --light-border: #e2e8f0;
            --light-text: #1e293b;
            --light-text-secondary: #64748b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Cairo', sans-serif;
            background: var(--dark-bg);
            color: var(--dark-text);
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        body.light-mode {
            background: var(--light-bg);
            color: var(--light-text);
        }

        .app-container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: var(--dark-surface);
            border-right: 1px solid var(--dark-border);
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .light-mode .sidebar {
            background: var(--light-surface);
            border-right: 1px solid var(--light-border);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--dark-border);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .light-mode .sidebar-header {
            border-bottom: 1px solid var(--light-border);
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .new-chat-btn {
            width: 100%;
            padding: 0.75rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 1rem;
        }

        .new-chat-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 1rem;
        }

        .conversation-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .conversation-item:hover {
            background: var(--dark-border);
        }

        .light-mode .conversation-item:hover {
            background: var(--light-border);
        }

        .conversation-item.active {
            background: var(--primary-color);
            color: white;
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-preview {
            font-size: 0.875rem;
            opacity: 0.7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Main Chat Area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .chat-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--dark-border);
            display: flex;
            align-items: center;
            justify-content: between;
            background: var(--dark-surface);
        }

        .light-mode .chat-header {
            background: var(--light-surface);
            border-bottom: 1px solid var(--light-border);
        }

        .chat-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--dark-text-secondary);
        }

        .light-mode .ai-status {
            color: var(--light-text-secondary);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-controls {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .control-btn {
            padding: 0.5rem;
            background: transparent;
            border: 1px solid var(--dark-border);
            border-radius: 8px;
            color: var(--dark-text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .light-mode .control-btn {
            border: 1px solid var(--light-border);
            color: var(--light-text-secondary);
        }

        .control-btn:hover {
            background: var(--dark-border);
            color: var(--dark-text);
        }

        .light-mode .control-btn:hover {
            background: var(--light-border);
            color: var(--light-text);
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 0.75rem;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .user-avatar {
            background: var(--accent-color);
            color: white;
        }

        .ai-avatar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .message-sender {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--dark-text-secondary);
        }

        .light-mode .message-time {
            color: var(--light-text-secondary);
        }

        .message-text {
            background: var(--dark-surface);
            padding: 1rem;
            border-radius: 12px;
            line-height: 1.6;
            word-wrap: break-word;
        }

        .light-mode .message-text {
            background: var(--light-surface);
        }

        .user-message .message-text {
            background: var(--primary-color);
            color: white;
            margin-left: auto;
            max-width: 80%;
        }

        .message-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            background: transparent;
            border: 1px solid var(--dark-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--dark-text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .light-mode .action-btn {
            border: 1px solid var(--light-border);
            color: var(--light-text-secondary);
        }

        .action-btn:hover {
            background: var(--dark-border);
        }

        .light-mode .action-btn:hover {
            background: var(--light-border);
        }

        /* Input Area */
        .input-container {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--dark-border);
            background: var(--dark-surface);
        }

        .light-mode .input-container {
            background: var(--light-surface);
            border-top: 1px solid var(--light-border);
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: end;
            gap: 0.75rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .message-input {
            flex: 1;
            min-height: 44px;
            max-height: 200px;
            padding: 0.75rem 1rem;
            background: var(--dark-bg);
            border: 1px solid var(--dark-border);
            border-radius: 12px;
            color: var(--dark-text);
            font-size: 0.875rem;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
        }

        .light-mode .message-input {
            background: var(--light-bg);
            border: 1px solid var(--light-border);
            color: var(--light-text);
        }

        .message-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .input-actions {
            display: flex;
            gap: 0.5rem;
        }

        .input-btn {
            width: 44px;
            height: 44px;
            background: var(--primary-color);
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .input-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .input-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.open {
                left: 0;
            }

            .chat-container {
                width: 100%;
            }

            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: none;
            }

            .mobile-overlay.show {
                display: block;
            }
        }

        /* Loading Animation */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            color: var(--dark-text-secondary);
            font-style: italic;
        }

        .light-mode .typing-indicator {
            color: var(--light-text-secondary);
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: typingDot 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingDot {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <div class="app-container" id="app">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">W</div>
                <div class="logo-text">WIDDX AI</div>
            </div>

            <button class="new-chat-btn" onclick="startNewChat()">
                <i class="fas fa-plus"></i>
                محادثة جديدة
            </button>

            <div class="conversations-list" id="conversationsList">
                <!-- Conversations will be loaded here -->
            </div>
        </div>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay" onclick="closeSidebar()"></div>

        <!-- Main Chat Area -->
        <div class="chat-container">
            <div class="chat-header">
                <div class="chat-title">
                    <button class="control-btn mobile-menu-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span id="chatTitle">WIDDX AI - مساعدك الذكي الخارق</span>
                </div>

                <div class="ai-status">
                    <div class="status-indicator"></div>
                    <span>متصل ونشط</span>
                </div>

                <div class="chat-controls">
                    <button class="control-btn" onclick="toggleTheme()" title="تبديل المظهر">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="control-btn" onclick="showSettings()" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="message ai-message">
                    <div class="message-avatar ai-avatar">W</div>
                    <div class="message-content">
                        <div class="message-header">
                            <span class="message-sender">WIDDX AI</span>
                            <span class="message-time">الآن</span>
                        </div>
                        <div class="message-text">
                            مرحباً! أنا **WIDDX AI** - مساعدك الذكي الخارق! 🚀✨<br><br>
                            أنا كيان ذكي متطور قادر على:<br>
                            - 🧠 **التفكير المنطقي** والتحليل العميق<br>
                            - 💻 **البرمجة والتطوير** بجميع اللغات<br>
                            - 🔍 **البحث والفهرسة** المتقدمة<br>
                            - 🎨 **الإبداع والابتكار** في الحلول<br>
                            - 📊 **تحليل البيانات** والاستنتاجات<br>
                            - 🌍 **التعلم المستمر** والتطور الذاتي<br><br>
                            أتدرب وأتعلم باستمرار لأقدم لك أفضل الحلول والإجابات. كيف يمكنني مساعدتك اليوم؟
                        </div>
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage(this)">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                            <button class="action-btn" onclick="regenerateResponse(this)">
                                <i class="fas fa-redo"></i> إعادة توليد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="input-container">
                <div class="input-wrapper">
                    <textarea
                        class="message-input"
                        id="messageInput"
                        placeholder="اكتب رسالتك هنا... (اضغط Enter للإرسال، Shift+Enter لسطر جديد)"
                        rows="1"
                        onkeydown="handleKeyDown(event)"
                        oninput="autoResize(this)"
                    ></textarea>

                    <div class="input-actions">
                        <button class="input-btn" onclick="attachFile()" title="إرفاق ملف">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button class="input-btn" id="sendBtn" onclick="sendMessage()" title="إرسال">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input -->
    <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp3,.wav,.csv,.xlsx,.json" onchange="handleFileSelect(event)">

    <script>
        // Global variables
        let currentConversationId = null;
        let isTyping = false;
        let conversations = [];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
            setupEventListeners();
        });

        // Event listeners
        function setupEventListeners() {
            // Auto-resize textarea
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('input', function() {
                autoResize(this);
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeSidebar();
                }
            });
        }

        // Theme management
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            if (body.classList.contains('dark-mode')) {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'light') {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                themeIcon.className = 'fas fa-sun';
            } else {
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                themeIcon.className = 'fas fa-moon';
            }
        }

        // Load theme on page load
        loadTheme();

        // Sidebar management
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('show');
            }
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
        }

        // Auto-resize textarea
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }

        // Handle keyboard input
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Send message
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message || isTyping) return;

            // Clear input
            messageInput.value = '';
            autoResize(messageInput);

            // Add user message to chat
            addMessage('user', message);

            // Show typing indicator
            showTypingIndicator();

            try {
                // Send to API
                const response = await fetch('/api/v2/conversations/' + (currentConversationId || '') + '/messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Authorization': 'Bearer ' + getAuthToken()
                    },
                    body: JSON.stringify({
                        message: message,
                        use_reasoning: true
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update conversation ID if new
                    if (!currentConversationId) {
                        currentConversationId = data.data.conversation_id;
                    }

                    // Add AI response
                    hideTypingIndicator();
                    addMessage('assistant', data.data.ai_message.content, {
                        reasoning_steps: data.data.reasoning_steps,
                        confidence_score: data.data.confidence_score,
                        tool_executions: data.data.tool_executions
                    });

                    // Update conversations list
                    loadConversations();
                } else {
                    hideTypingIndicator();
                    addMessage('assistant', 'عذراً، حدث خطأ في معالجة رسالتك. يرجى المحاولة مرة أخرى.');
                }
            } catch (error) {
                console.error('Error sending message:', error);
                hideTypingIndicator();
                addMessage('assistant', 'عذراً، حدث خطأ في الاتصال. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
            }
        }

        // Add message to chat
        function addMessage(role, content, metadata = {}) {
            const messagesContainer = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;

            const avatar = role === 'user' ? 'U' : 'W';
            const avatarClass = role === 'user' ? 'user-avatar' : 'ai-avatar';
            const senderName = role === 'user' ? 'أنت' : 'WIDDX AI';
            const currentTime = new Date().toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });

            let reasoningSteps = '';
            if (metadata.reasoning_steps && metadata.reasoning_steps.length > 0) {
                reasoningSteps = '<div class="reasoning-steps" style="margin-top: 1rem; padding: 1rem; background: rgba(99, 102, 241, 0.1); border-radius: 8px; border-left: 3px solid var(--primary-color);">';
                reasoningSteps += '<h4 style="margin-bottom: 0.5rem; color: var(--primary-color);"><i class="fas fa-brain"></i> خطوات التفكير:</h4>';
                metadata.reasoning_steps.forEach((step, index) => {
                    reasoningSteps += `<div style="margin-bottom: 0.5rem; font-size: 0.875rem;">`;
                    reasoningSteps += `<strong>الخطوة ${step.step}:</strong> ${step.description}<br>`;
                    reasoningSteps += `<span style="opacity: 0.8;">${step.content}</span>`;
                    reasoningSteps += `</div>`;
                });
                reasoningSteps += '</div>';
            }

            let toolExecutions = '';
            if (metadata.tool_executions && metadata.tool_executions.length > 0) {
                toolExecutions = '<div class="tool-executions" style="margin-top: 1rem; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 8px; border-left: 3px solid var(--success-color);">';
                toolExecutions += '<h4 style="margin-bottom: 0.5rem; color: var(--success-color);"><i class="fas fa-tools"></i> الأدوات المستخدمة:</h4>';
                metadata.tool_executions.forEach(execution => {
                    const statusIcon = execution.status === 'success' ? 'fa-check-circle' : 'fa-times-circle';
                    const statusColor = execution.status === 'success' ? 'var(--success-color)' : 'var(--error-color)';
                    toolExecutions += `<div style="margin-bottom: 0.5rem; font-size: 0.875rem;">`;
                    toolExecutions += `<i class="fas ${statusIcon}" style="color: ${statusColor};"></i> `;
                    toolExecutions += `<strong>${execution.tool_name}:</strong> ${execution.summary || 'تم التنفيذ'}`;
                    toolExecutions += `</div>`;
                });
                toolExecutions += '</div>';
            }

            let confidenceScore = '';
            if (metadata.confidence_score) {
                const confidence = Math.round(metadata.confidence_score * 100);
                const confidenceColor = confidence >= 80 ? 'var(--success-color)' : confidence >= 60 ? 'var(--warning-color)' : 'var(--error-color)';
                confidenceScore = `<div style="margin-top: 0.5rem; font-size: 0.75rem; color: ${confidenceColor};">`;
                confidenceScore += `<i class="fas fa-chart-line"></i> مستوى الثقة: ${confidence}%`;
                confidenceScore += `</div>`;
            }

            messageDiv.innerHTML = `
                <div class="message-avatar ${avatarClass}">${avatar}</div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-sender">${senderName}</span>
                        <span class="message-time">${currentTime}</span>
                    </div>
                    <div class="message-text">
                        ${formatMessage(content)}
                        ${reasoningSteps}
                        ${toolExecutions}
                        ${confidenceScore}
                    </div>
                    ${role === 'assistant' ? `
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage(this)">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                            <button class="action-btn" onclick="regenerateResponse(this)">
                                <i class="fas fa-redo"></i> إعادة توليد
                            </button>
                            <button class="action-btn" onclick="showReasoningDetails(this)">
                                <i class="fas fa-brain"></i> التفاصيل
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Format message content
        function formatMessage(content) {
            // Convert markdown-like formatting
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            content = content.replace(/```([\s\S]*?)```/g, '<pre style="background: rgba(0,0,0,0.1); padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 0.5rem 0;"><code>$1</code></pre>');
            content = content.replace(/`(.*?)`/g, '<code style="background: rgba(0,0,0,0.1); padding: 0.2rem 0.4rem; border-radius: 4px;">$1</code>');
            content = content.replace(/\n/g, '<br>');

            return content;
        }

        // Show typing indicator
        function showTypingIndicator() {
            isTyping = true;
            const messagesContainer = document.getElementById('messagesContainer');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typingIndicator';
            typingDiv.innerHTML = `
                <div class="message-avatar ai-avatar">W</div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span>WIDDX AI يفكر</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            isTyping = false;
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Load conversations
        async function loadConversations() {
            try {
                const response = await fetch('/api/v2/conversations', {
                    headers: {
                        'Authorization': 'Bearer ' + getAuthToken()
                    }
                });

                const data = await response.json();

                if (data.success) {
                    conversations = data.data.data;
                    renderConversations();
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
            }
        }

        // Render conversations list
        function renderConversations() {
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = '';

            conversations.forEach(conversation => {
                const conversationDiv = document.createElement('div');
                conversationDiv.className = `conversation-item ${conversation.id === currentConversationId ? 'active' : ''}`;
                conversationDiv.onclick = () => loadConversation(conversation.id);

                const lastMessage = conversation.messages && conversation.messages.length > 0
                    ? conversation.messages[0].content.substring(0, 50) + '...'
                    : 'محادثة جديدة';

                conversationDiv.innerHTML = `
                    <div class="conversation-title">${conversation.title}</div>
                    <div class="conversation-preview">${lastMessage}</div>
                `;

                conversationsList.appendChild(conversationDiv);
            });
        }

        // Load specific conversation
        async function loadConversation(conversationId) {
            try {
                currentConversationId = conversationId;

                // Update active conversation in sidebar
                document.querySelectorAll('.conversation-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.closest('.conversation-item').classList.add('active');

                // Load messages
                const response = await fetch(`/api/v2/conversations/${conversationId}/messages`, {
                    headers: {
                        'Authorization': 'Bearer ' + getAuthToken()
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Clear current messages
                    const messagesContainer = document.getElementById('messagesContainer');
                    messagesContainer.innerHTML = '';

                    // Add messages
                    data.data.data.forEach(message => {
                        addMessage(message.role, message.content, {
                            reasoning_steps: message.reasoning_steps,
                            confidence_score: message.confidence_score,
                            tool_calls: message.tool_calls
                        });
                    });
                }

                // Close sidebar on mobile
                if (window.innerWidth <= 768) {
                    closeSidebar();
                }
            } catch (error) {
                console.error('Error loading conversation:', error);
            }
        }

        // Start new chat
        function startNewChat() {
            currentConversationId = null;

            // Clear messages
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.innerHTML = `
                <div class="message ai-message">
                    <div class="message-avatar ai-avatar">W</div>
                    <div class="message-content">
                        <div class="message-header">
                            <span class="message-sender">WIDDX AI</span>
                            <span class="message-time">الآن</span>
                        </div>
                        <div class="message-text">
                            مرحباً! أنا **WIDDX AI** - مساعدك الذكي الخارق! 🚀✨<br><br>
                            كيف يمكنني مساعدتك اليوم؟
                        </div>
                    </div>
                </div>
            `;

            // Update title
            document.getElementById('chatTitle').textContent = 'WIDDX AI - محادثة جديدة';

            // Remove active class from conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });

            // Close sidebar on mobile
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        }

        // Message actions
        function copyMessage(button) {
            const messageText = button.closest('.message-content').querySelector('.message-text');
            const text = messageText.innerText;

            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }

        function regenerateResponse(button) {
            // Implementation for regenerating response
            console.log('Regenerating response...');
        }

        function showReasoningDetails(button) {
            // Implementation for showing detailed reasoning
            console.log('Showing reasoning details...');
        }

        // File handling
        function attachFile() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                // Handle file upload
                console.log('Files selected:', files);
                // Implementation for file upload
            }
        }

        // Settings
        function showSettings() {
            // Implementation for settings modal
            console.log('Showing settings...');
        }

        // Get auth token (implement based on your auth system)
        function getAuthToken() {
            // Return the user's auth token
            return localStorage.getItem('auth_token') || '';
        }
    </script>
</body>
</html>
<?php /**PATH D:\WIDDX-AI\widdx-ai\resources\views/chat/index.blade.php ENDPATH**/ ?>