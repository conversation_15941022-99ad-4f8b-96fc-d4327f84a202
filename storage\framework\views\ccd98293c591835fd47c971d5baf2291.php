<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - Your Super Smart AI Assistant</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body>
    <!-- WIDDX AI v2.0 Application Container -->
    <div id="app">
        <widdx-ai-chat></widdx-ai-chat>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #0f172a; display: flex; align-items: center; justify-content: center; z-index: 9999;">
        <div style="text-align: center; color: #f1f5f9;">
            <div style="width: 60px; height: 60px; border: 4px solid rgba(99, 102, 241, 0.3); border-radius: 50%; border-top-color: #6366f1; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
            <h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem; background: linear-gradient(135deg, #6366f1, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">WIDDX AI</h2>
            <p style="color: #94a3b8;">Loading your super smart AI assistant...</p>
        </div>
    </div>

    <style>
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Hide loading screen when Vue is ready */
        .vue-ready #loading-screen {
            display: none !important;
        }
    </style>

    <script>
        // Hide loading screen when Vue app is mounted
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.body.classList.add('vue-ready');
            }, 1000);
        });

        // Initialize theme from localStorage
        const savedTheme = localStorage.getItem('widdx-theme') || 'dark';
        document.body.classList.add(savedTheme + '-mode');

        // Initialize language and direction
        const savedLanguage = localStorage.getItem('widdx-language') || 'en';
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        document.documentElement.lang = savedLanguage;
        document.documentElement.dir = rtlLanguages.includes(savedLanguage) ? 'rtl' : 'ltr';
        document.body.classList.add(rtlLanguages.includes(savedLanguage) ? 'rtl' : 'ltr');
    </script>
</body>
</html>
<?php /**PATH D:\WIDDX-AI\widdx-ai\resources\views/chat/index.blade.php ENDPATH**/ ?>