<template>
  <div class="sidebar" :class="{ 'open': open }">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
      <div class="logo">
        <i class="fas fa-brain"></i>
      </div>
      <div class="logo-text">{{ $t('app.name') }}</div>
      <button class="theme-toggle" @click="appStore.toggleTheme">
        <i :class="appStore.isDark ? 'fas fa-sun' : 'fas fa-moon'"></i>
      </button>
    </div>
    
    <!-- New Chat Button -->
    <button class="new-chat-btn" @click="$emit('new-chat')">
      <i class="fas fa-plus"></i>
      {{ $t('navigation.newChat') }}
    </button>
    
    <!-- Conversations List -->
    <div class="conversations-list">
      <div 
        v-for="conversation in chatStore.conversations" 
        :key="conversation.id"
        class="conversation-item"
        :class="{ 'active': conversation.id === chatStore.currentConversation?.id }"
        @click="$emit('select-conversation', conversation.id)"
      >
        <div class="conversation-title">{{ conversation.title || $t('navigation.newChat') }}</div>
        <div class="conversation-preview">{{ getLastMessagePreview(conversation) }}</div>
        <div class="conversation-time">{{ formatTime(conversation.updated_at) }}</div>
        
        <!-- Conversation Actions -->
        <div class="conversation-actions">
          <button 
            class="action-btn"
            @click.stop="deleteConversation(conversation.id)"
            :title="$t('common.delete')"
          >
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-if="chatStore.conversations.length === 0" class="empty-conversations">
        <div class="empty-icon">
          <i class="fas fa-comments"></i>
        </div>
        <p>{{ $t('messages.noMessages') }}</p>
      </div>
    </div>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
      <div class="user-info">
        <div class="user-avatar">
          <i class="fas fa-user"></i>
        </div>
        <div class="user-details">
          <div class="user-name">User</div>
          <div class="user-status">{{ $t('status.online') }}</div>
        </div>
      </div>
      
      <div class="sidebar-actions">
        <button class="sidebar-action-btn" @click="showSettings = true" :title="$t('settings.title')">
          <i class="fas fa-cog"></i>
        </button>
        <button class="sidebar-action-btn" @click="toggleLanguage" :title="$t('settings.language')">
          <i class="fas fa-language"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '../../stores/app'
import { useChatStore } from '../../stores/chat'
import { getAvailableLanguages, setLanguage, getCurrentLanguage } from '../../i18n'

// Props
defineProps({
  open: {
    type: Boolean,
    default: true
  }
})

// Emits
defineEmits(['toggle', 'new-chat', 'select-conversation'])

// Composables
const { t, locale } = useI18n()
const appStore = useAppStore()
const chatStore = useChatStore()

// Reactive state
const showSettings = ref(false)

// Computed
const availableLanguages = computed(() => getAvailableLanguages())

// Methods
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const getLastMessagePreview = (conversation) => {
  if (!conversation.last_message) return t('messages.noMessages')
  
  const content = conversation.last_message.content || ''
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

const deleteConversation = async (conversationId) => {
  if (confirm(t('common.confirm'))) {
    await chatStore.deleteConversation(conversationId)
  }
}

const toggleLanguage = () => {
  const languages = availableLanguages.value
  const currentIndex = languages.findIndex(lang => lang.code === getCurrentLanguage())
  const nextIndex = (currentIndex + 1) % languages.length
  const nextLanguage = languages[nextIndex].code
  
  appStore.setLanguageAndDirection(nextLanguage)
  locale.value = nextLanguage
}
</script>

<style scoped>
.sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 100;
  position: relative;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--bg-secondary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.logo:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  flex: 1;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.theme-toggle:hover {
  background: var(--primary-500);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.new-chat-btn {
  margin: 1rem 1.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.new-chat-btn:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.new-chat-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 1rem;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.conversations-list::-webkit-scrollbar {
  width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
  background: transparent;
}

.conversations-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

.conversation-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  group: true;
}

.conversation-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(4px);
}

.conversation-item.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: var(--primary-600);
}

.conversation-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.conversation-preview {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.conversation-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.action-btn {
  width: 24px;
  height: 24px;
  background: var(--error-500);
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.action-btn:hover {
  background: var(--error-600);
  transform: scale(1.1);
}

.empty-conversations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-icon {
  width: 48px;
  height: 48px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: var(--accent-500);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.sidebar-actions {
  display: flex;
  gap: 0.5rem;
}

.sidebar-action-btn {
  width: 32px;
  height: 32px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.sidebar-action-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -320px;
    height: 100vh;
    z-index: 1000;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
  
  .sidebar.open {
    left: 0;
  }
}
</style>
