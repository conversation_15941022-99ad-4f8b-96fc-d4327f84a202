{"app": {"name": "WIDDX AI", "tagline": "Your Super Smart AI Assistant", "version": "v2.0"}, "navigation": {"newChat": "New Chat", "conversations": "Conversations", "settings": "Settings", "profile": "Profile", "admin": "Admin Panel", "logout": "Logout"}, "chat": {"title": "WIDDX AI - Your Super Smart Assistant", "placeholder": "Type your message here... (Press Enter to send, Shift+Enter for new line)", "send": "Send", "sending": "Sending...", "thinking": "WIDDX AI is thinking", "typing": "Typing...", "regenerate": "Regenerate", "copy": "Copy", "copied": "Copied!", "delete": "Delete", "edit": "Edit", "share": "Share", "export": "Export", "like": "Like", "dislike": "Dislike", "report": "Report"}, "messages": {"you": "You", "assistant": "WIDDX AI", "system": "System", "welcome": "Hello! I'm **WIDDX AI** - your super smart AI assistant! 🚀✨\n\nI'm an advanced intelligent entity capable of:\n- 🧠 **Logical thinking** and deep analysis\n- 💻 **Programming and development** in all languages\n- 🔍 **Advanced search** and indexing\n- 🎨 **Creativity and innovation** in solutions\n- 📊 **Data analysis** and insights\n- 🌍 **Continuous learning** and self-evolution\n\nI continuously train and learn to provide you with the best solutions and answers. How can I help you today?", "errorGeneral": "Sorry, an error occurred while processing your message. Please try again.", "errorNetwork": "Sorry, a connection error occurred. Please check your internet connection and try again.", "errorTimeout": "The request timed out. Please try again.", "noMessages": "No messages yet. Start a conversation!", "loadingMore": "Loading more messages..."}, "reasoning": {"title": "Reasoning Steps", "step": "Step", "confidence": "Confidence", "analysis": "Analysis", "planning": "Planning", "execution": "Execution", "synthesis": "Synthesis", "conclusion": "Conclusion", "showDetails": "Show Details", "hideDetails": "Hide Details"}, "tools": {"title": "Tools Used", "calculator": "Calculator", "webSearch": "Web Search", "codeAnalyzer": "Code Analyzer", "dataAnalyzer": "Data Analyzer", "summarizer": "Summa<PERSON><PERSON>", "translator": "Translator", "imageAnalyzer": "Image Analyzer", "success": "Success", "failed": "Failed", "running": "Running", "pending": "Pending"}, "files": {"upload": "Upload File", "dragDrop": "Drag and drop files here or click to browse", "supported": "Supported formats", "maxSize": "Max size", "processing": "Processing...", "processed": "Processed", "failed": "Failed to process", "remove": "Remove", "download": "Download", "preview": "Preview"}, "personality": {"title": "AI Personality", "tone": "<PERSON><PERSON>", "verbosity": "Verbosity", "creativity": "Creativity", "formality": "Formality", "tones": {"friendly": "Friendly", "professional": "Professional", "casual": "Casual", "enthusiastic": "Enthusiastic", "calm": "Calm"}, "verbosityLevels": {"brief": "Brief", "normal": "Normal", "detailed": "Detailed", "comprehensive": "Comprehensive"}, "creativityLevels": {"conservative": "Conservative", "balanced": "Balanced", "creative": "Creative", "innovative": "Innovative"}, "formalityLevels": {"informal": "Informal", "neutral": "Neutral", "formal": "Formal", "academic": "Academic"}}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "advanced": "Advanced", "themes": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "save": "Save", "cancel": "Cancel", "reset": "Reset to De<PERSON>ult", "export": "Export Settings", "import": "Import Settings"}, "status": {"online": "Online", "offline": "Offline", "connecting": "Connecting", "connected": "Connected and Active", "error": "Error", "maintenance": "Under Maintenance"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "retry": "Retry", "more": "More", "less": "Less", "all": "All", "none": "None", "yes": "Yes", "no": "No", "ok": "OK"}, "time": {"now": "Now", "justNow": "Just now", "minuteAgo": "A minute ago", "minutesAgo": "{count} minutes ago", "hourAgo": "An hour ago", "hoursAgo": "{count} hours ago", "dayAgo": "A day ago", "daysAgo": "{count} days ago", "weekAgo": "A week ago", "weeksAgo": "{count} weeks ago", "monthAgo": "A month ago", "monthsAgo": "{count} months ago", "yearAgo": "A year ago", "yearsAgo": "{count} years ago"}}