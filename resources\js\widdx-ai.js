import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import { i18n } from './i18n'
import WiddxAIChat from './components/WiddxAIChat.vue'

// Import CSS
import '../css/widdx-ai-advanced.css'

// Create Vue app
const app = createApp(WiddxAIChat)

// Use plugins
app.use(createPinia())
app.use(i18n)

// Global properties
app.config.globalProperties.$csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')

// Error handling
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  
  // Send error to monitoring service if available
  if (window.Sentry) {
    window.Sentry.captureException(err)
  }
}

// Mount app
app.mount('#widdx-ai-app')

// Global utilities
window.WiddxAI = {
  version: '2.0.0',
  app,
  
  // Utility functions
  utils: {
    formatTime: (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    },
    
    formatDate: (timestamp) => {
      return new Date(timestamp).toLocaleDateString()
    },
    
    copyToClipboard: async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        return true
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
        return false
      }
    },
    
    downloadFile: (content, filename, type = 'text/plain') => {
      const blob = new Blob([content], { type })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    },
    
    debounce: (func, wait) => {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },
    
    throttle: (func, limit) => {
      let inThrottle
      return function() {
        const args = arguments
        const context = this
        if (!inThrottle) {
          func.apply(context, args)
          inThrottle = true
          setTimeout(() => inThrottle = false, limit)
        }
      }
    }
  },
  
  // API helpers
  api: {
    baseURL: '/api/v2',
    
    async request(endpoint, options = {}) {
      const url = `${this.baseURL}${endpoint}`
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': window.WiddxAI.app.config.globalProperties.$csrfToken,
          ...options.headers
        },
        ...options
      }
      
      try {
        const response = await fetch(url, config)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        return data
      } catch (error) {
        console.error('API Request failed:', error)
        throw error
      }
    },
    
    async get(endpoint, params = {}) {
      const url = new URL(`${this.baseURL}${endpoint}`, window.location.origin)
      Object.keys(params).forEach(key => url.searchParams.append(key, params[key]))
      
      return this.request(url.pathname + url.search, { method: 'GET' })
    },
    
    async post(endpoint, data = {}) {
      return this.request(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    
    async put(endpoint, data = {}) {
      return this.request(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    
    async delete(endpoint) {
      return this.request(endpoint, { method: 'DELETE' })
    },
    
    async upload(endpoint, formData) {
      return this.request(endpoint, {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': window.WiddxAI.app.config.globalProperties.$csrfToken
        },
        body: formData
      })
    }
  },
  
  // Event system
  events: {
    listeners: {},
    
    on(event, callback) {
      if (!this.listeners[event]) {
        this.listeners[event] = []
      }
      this.listeners[event].push(callback)
    },
    
    off(event, callback) {
      if (!this.listeners[event]) return
      
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    },
    
    emit(event, data) {
      if (!this.listeners[event]) return
      
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Event listener error:', error)
        }
      })
    }
  },
  
  // Storage helpers
  storage: {
    get(key, defaultValue = null) {
      try {
        const item = localStorage.getItem(`widdx-ai-${key}`)
        return item ? JSON.parse(item) : defaultValue
      } catch (error) {
        console.error('Storage get error:', error)
        return defaultValue
      }
    },
    
    set(key, value) {
      try {
        localStorage.setItem(`widdx-ai-${key}`, JSON.stringify(value))
        return true
      } catch (error) {
        console.error('Storage set error:', error)
        return false
      }
    },
    
    remove(key) {
      try {
        localStorage.removeItem(`widdx-ai-${key}`)
        return true
      } catch (error) {
        console.error('Storage remove error:', error)
        return false
      }
    },
    
    clear() {
      try {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('widdx-ai-')) {
            localStorage.removeItem(key)
          }
        })
        return true
      } catch (error) {
        console.error('Storage clear error:', error)
        return false
      }
    }
  }
}

// Initialize performance monitoring
if (window.performance && window.performance.mark) {
  window.performance.mark('widdx-ai-init-start')
  
  app.config.globalProperties.$nextTick(() => {
    window.performance.mark('widdx-ai-init-end')
    window.performance.measure('widdx-ai-init', 'widdx-ai-init-start', 'widdx-ai-init-end')
    
    const measure = window.performance.getEntriesByName('widdx-ai-init')[0]
    console.log(`WIDDX AI initialized in ${measure.duration.toFixed(2)}ms`)
  })
}

// Service Worker registration (if available)
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration)
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

// Keyboard shortcuts
document.addEventListener('keydown', (event) => {
  // Global shortcuts
  if ((event.ctrlKey || event.metaKey)) {
    switch (event.key) {
      case 'k':
        event.preventDefault()
        window.WiddxAI.events.emit('focus-search')
        break
      case 'n':
        event.preventDefault()
        window.WiddxAI.events.emit('new-chat')
        break
      case ',':
        event.preventDefault()
        window.WiddxAI.events.emit('open-settings')
        break
      case '/':
        event.preventDefault()
        window.WiddxAI.events.emit('toggle-shortcuts-help')
        break
    }
  }
  
  // Escape key
  if (event.key === 'Escape') {
    window.WiddxAI.events.emit('escape-pressed')
  }
})

// Online/offline detection
window.addEventListener('online', () => {
  window.WiddxAI.events.emit('connection-status', { online: true })
})

window.addEventListener('offline', () => {
  window.WiddxAI.events.emit('connection-status', { online: false })
})

// Visibility change detection
document.addEventListener('visibilitychange', () => {
  window.WiddxAI.events.emit('visibility-change', { 
    visible: !document.hidden 
  })
})

// Unload warning for unsaved changes
window.addEventListener('beforeunload', (event) => {
  // Check if there are unsaved changes
  const hasUnsavedChanges = window.WiddxAI.storage.get('has-unsaved-changes', false)
  
  if (hasUnsavedChanges) {
    event.preventDefault()
    event.returnValue = ''
    return ''
  }
})

console.log('🚀 WIDDX AI v2.0 - Next Generation AI Assistant Loaded!')
console.log('🧠 Advanced features: Multi-language, Chain of Thought, Tool Execution')
console.log('🌍 Supported languages: English, Arabic, French, Spanish, German')
console.log('🎨 Themes: Dark/Light mode with RTL/LTR support')
console.log('📱 Responsive design for all devices')
console.log('🔧 Developer tools available at window.WiddxAI')
