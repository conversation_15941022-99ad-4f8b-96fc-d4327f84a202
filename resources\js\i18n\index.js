import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import ar from './locales/ar.json'
import fr from './locales/fr.json'
import es from './locales/es.json'
import de from './locales/de.json'

// Get saved language or detect from browser
const savedLanguage = localStorage.getItem('widdx-language')
const browserLanguage = navigator.language.split('-')[0]
const defaultLanguage = savedLanguage || (ar[browserLanguage] ? browserLanguage : 'en')

// RTL languages
const rtlLanguages = ['ar', 'he', 'fa', 'ur']

export const i18n = createI18n({
  legacy: false,
  locale: defaultLanguage,
  fallbackLocale: 'en',
  messages: {
    en,
    ar,
    fr,
    es,
    de
  },
  globalInjection: true,
  warnHtmlMessage: false
})

// Helper functions
export const setLanguage = (language) => {
  i18n.global.locale.value = language
  localStorage.setItem('widdx-language', language)
  
  // Update document direction and language
  document.documentElement.lang = language
  document.documentElement.dir = rtlLanguages.includes(language) ? 'rtl' : 'ltr'
  
  // Update body class for styling
  document.body.classList.remove('rtl', 'ltr')
  document.body.classList.add(rtlLanguages.includes(language) ? 'rtl' : 'ltr')
}

export const getCurrentLanguage = () => i18n.global.locale.value

export const isRTL = (language = null) => {
  const lang = language || getCurrentLanguage()
  return rtlLanguages.includes(lang)
}

export const getAvailableLanguages = () => [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' }
]

// Initialize language on load
setLanguage(defaultLanguage)
