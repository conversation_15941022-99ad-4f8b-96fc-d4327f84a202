<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <!-- <PERSON>dal Header -->
      <div class="modal-header">
        <div class="file-info">
          <i :class="getFileIcon(file.type)" class="file-icon"></i>
          <div class="file-details">
            <h3>{{ file.name }}</h3>
            <p class="file-meta">{{ formatFileSize(file.size) }} • {{ file.type }}</p>
          </div>
        </div>
        
        <div class="header-actions">
          <button class="action-btn" @click="downloadFile" :title="$t('files.download')">
            <i class="fas fa-download"></i>
          </button>
          <button class="action-btn" @click="$emit('close')" :title="$t('common.close')">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <!-- Modal Body -->
      <div class="modal-body">
        <!-- Image Preview -->
        <div v-if="isImage" class="image-preview">
          <img :src="fileUrl" :alt="file.name" @load="onImageLoad" @error="onImageError">
          <div v-if="imageLoading" class="loading-overlay">
            <div class="spinner"></div>
            <p>{{ $t('files.loading') }}</p>
          </div>
        </div>
        
        <!-- Text Preview -->
        <div v-else-if="isText" class="text-preview">
          <div v-if="textContent" class="text-content">
            <pre><code>{{ textContent }}</code></pre>
          </div>
          <div v-else class="loading-overlay">
            <div class="spinner"></div>
            <p>{{ $t('files.loading') }}</p>
          </div>
        </div>
        
        <!-- PDF Preview -->
        <div v-else-if="isPDF" class="pdf-preview">
          <iframe :src="fileUrl" frameborder="0"></iframe>
        </div>
        
        <!-- Video Preview -->
        <div v-else-if="isVideo" class="video-preview">
          <video :src="fileUrl" controls preload="metadata">
            {{ $t('files.videoNotSupported') }}
          </video>
        </div>
        
        <!-- Audio Preview -->
        <div v-else-if="isAudio" class="audio-preview">
          <div class="audio-player">
            <i class="fas fa-music audio-icon"></i>
            <audio :src="fileUrl" controls preload="metadata">
              {{ $t('files.audioNotSupported') }}
            </audio>
          </div>
        </div>
        
        <!-- Unsupported File Type -->
        <div v-else class="unsupported-preview">
          <div class="unsupported-content">
            <i :class="getFileIcon(file.type)" class="large-icon"></i>
            <h4>{{ $t('files.previewNotAvailable') }}</h4>
            <p>{{ $t('files.previewNotSupported', { type: file.type }) }}</p>
            <button class="btn btn-primary" @click="downloadFile">
              <i class="fas fa-download"></i>
              {{ $t('files.download') }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- Modal Footer -->
      <div class="modal-footer" v-if="showFooter">
        <div class="file-actions">
          <button class="btn btn-outline" @click="copyFileInfo">
            <i class="fas fa-copy"></i>
            {{ $t('files.copyInfo') }}
          </button>
          
          <button class="btn btn-outline" @click="shareFile" v-if="canShare">
            <i class="fas fa-share"></i>
            {{ $t('files.share') }}
          </button>
          
          <button class="btn btn-primary" @click="downloadFile">
            <i class="fas fa-download"></i>
            {{ $t('files.download') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

// Props
const props = defineProps({
  file: {
    type: Object,
    required: true
  }
})

// Emits
defineEmits(['close'])

// Composables
const { t } = useI18n()

// Reactive state
const fileUrl = ref('')
const textContent = ref('')
const imageLoading = ref(true)

// Computed
const isImage = computed(() => props.file.type.startsWith('image/'))
const isText = computed(() => {
  return props.file.type.startsWith('text/') || 
         props.file.type.includes('json') ||
         props.file.type.includes('javascript') ||
         props.file.type.includes('css') ||
         props.file.name.endsWith('.md') ||
         props.file.name.endsWith('.txt')
})
const isPDF = computed(() => props.file.type.includes('pdf'))
const isVideo = computed(() => props.file.type.startsWith('video/'))
const isAudio = computed(() => props.file.type.startsWith('audio/'))

const showFooter = computed(() => {
  return isImage.value || isText.value || isPDF.value
})

const canShare = computed(() => {
  return navigator.share && (isImage.value || isText.value)
})

// Methods
const getFileIcon = (fileType) => {
  if (fileType.startsWith('image/')) return 'fas fa-image'
  if (fileType.startsWith('video/')) return 'fas fa-video'
  if (fileType.startsWith('audio/')) return 'fas fa-music'
  if (fileType.includes('pdf')) return 'fas fa-file-pdf'
  if (fileType.includes('word')) return 'fas fa-file-word'
  if (fileType.includes('excel') || fileType.includes('spreadsheet')) return 'fas fa-file-excel'
  if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'fas fa-file-powerpoint'
  if (fileType.includes('text') || fileType.includes('json')) return 'fas fa-file-alt'
  if (fileType.includes('zip') || fileType.includes('archive')) return 'fas fa-file-archive'
  return 'fas fa-file'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const createFileUrl = () => {
  if (props.file.file) {
    fileUrl.value = URL.createObjectURL(props.file.file)
  } else if (props.file.url) {
    fileUrl.value = props.file.url
  }
}

const loadTextContent = async () => {
  if (props.file.file && isText.value) {
    try {
      const text = await props.file.file.text()
      textContent.value = text
    } catch (error) {
      console.error('Failed to load text content:', error)
      textContent.value = t('files.loadError')
    }
  }
}

const onImageLoad = () => {
  imageLoading.value = false
}

const onImageError = () => {
  imageLoading.value = false
  console.error('Failed to load image')
}

const downloadFile = () => {
  if (fileUrl.value) {
    const a = document.createElement('a')
    a.href = fileUrl.value
    a.download = props.file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
}

const copyFileInfo = async () => {
  try {
    const info = `File: ${props.file.name}\nSize: ${formatFileSize(props.file.size)}\nType: ${props.file.type}`
    await navigator.clipboard.writeText(info)
    console.log('File info copied to clipboard')
  } catch (error) {
    console.error('Failed to copy file info:', error)
  }
}

const shareFile = async () => {
  if (navigator.share && fileUrl.value) {
    try {
      await navigator.share({
        title: props.file.name,
        text: `Sharing file: ${props.file.name}`,
        url: fileUrl.value
      })
    } catch (error) {
      console.error('Failed to share file:', error)
    }
  }
}

// Lifecycle
onMounted(() => {
  createFileUrl()
  if (isText.value) {
    loadTextContent()
  }
})

onUnmounted(() => {
  if (fileUrl.value && fileUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(fileUrl.value)
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 1.5rem;
  color: var(--primary-500);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-details h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Image Preview */
.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  position: relative;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
}

/* Text Preview */
.text-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: var(--bg-secondary);
}

.text-content {
  padding: 1.5rem;
}

.text-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-primary);
}

.text-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  color: inherit;
}

/* PDF Preview */
.pdf-preview {
  width: 100%;
  height: 500px;
}

.pdf-preview iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Video Preview */
.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  padding: 1rem;
}

.video-preview video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 0.5rem;
}

/* Audio Preview */
.audio-preview {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  padding: 2rem;
}

.audio-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.audio-icon {
  font-size: 3rem;
  color: var(--primary-500);
}

.audio-player audio {
  width: 300px;
  max-width: 100%;
}

/* Unsupported Preview */
.unsupported-preview {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  padding: 2rem;
}

.unsupported-content {
  text-align: center;
  max-width: 400px;
}

.large-icon {
  font-size: 4rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.unsupported-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.unsupported-content p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top-color: var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-overlay p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Modal Footer */
.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.file-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 1rem;
  }
  
  .file-details h3 {
    font-size: 0.875rem;
  }
  
  .file-meta {
    font-size: 0.75rem;
  }
  
  .action-btn {
    width: 32px;
    height: 32px;
  }
  
  .text-content {
    padding: 1rem;
  }
  
  .text-content pre {
    font-size: 0.75rem;
  }
  
  .pdf-preview {
    height: 400px;
  }
  
  .audio-player audio {
    width: 250px;
  }
  
  .file-actions {
    flex-direction: column;
  }
  
  .modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .file-info {
    gap: 0.75rem;
  }
  
  .file-icon {
    font-size: 1.25rem;
  }
  
  .header-actions {
    gap: 0.25rem;
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .unsupported-content {
    padding: 1rem;
  }
  
  .large-icon {
    font-size: 3rem;
  }
  
  .audio-player audio {
    width: 200px;
  }
}
</style>
