<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AiPersonality extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'traits',
        'preferences',
        'is_active'
    ];

    protected $casts = [
        'traits' => 'array',
        'preferences' => 'array',
        'is_active' => 'boolean'
    ];

    /**
     * Get user personalities using this AI personality
     */
    public function userPersonalities(): HasMany
    {
        return $this->hasMany(UserPersonality::class);
    }

    /**
     * Get conversations using this personality
     */
    public function conversations(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Conversation::class);
    }

    /**
     * Scope for active personalities
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
