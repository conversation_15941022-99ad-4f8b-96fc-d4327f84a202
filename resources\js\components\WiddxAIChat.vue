<template>
  <div class="widdx-ai-chat" :class="{ 'light-mode': isLightMode }">
    <!-- Sidebar -->
    <div class="sidebar" :class="{ 'open': sidebarOpen }">
      <div class="sidebar-header">
        <div class="logo">W</div>
        <div class="logo-text">WIDDX AI</div>
        <button class="theme-toggle" @click="toggleTheme">
          <i :class="isLightMode ? 'fas fa-moon' : 'fas fa-sun'"></i>
        </button>
      </div>
      
      <button class="new-chat-btn" @click="startNewChat">
        <i class="fas fa-plus"></i>
        {{ $t('newChat') }}
      </button>
      
      <div class="conversations-list">
        <div 
          v-for="conversation in conversations" 
          :key="conversation.id"
          class="conversation-item"
          :class="{ 'active': conversation.id === currentConversationId }"
          @click="loadConversation(conversation.id)"
        >
          <div class="conversation-title">{{ conversation.title }}</div>
          <div class="conversation-preview">{{ getLastMessagePreview(conversation) }}</div>
          <div class="conversation-meta">
            <span class="conversation-time">{{ formatTime(conversation.last_activity_at) }}</span>
            <div class="conversation-actions">
              <button @click.stop="editConversationTitle(conversation)" class="action-icon">
                <i class="fas fa-edit"></i>
              </button>
              <button @click.stop="deleteConversation(conversation.id)" class="action-icon">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="sidebar-footer">
        <button class="settings-btn" @click="showSettings = true">
          <i class="fas fa-cog"></i>
          {{ $t('settings') }}
        </button>
      </div>
    </div>

    <!-- Mobile Overlay -->
    <div 
      class="mobile-overlay" 
      :class="{ 'show': sidebarOpen }" 
      @click="sidebarOpen = false"
    ></div>

    <!-- Main Chat Area -->
    <div class="chat-container">
      <!-- Chat Header -->
      <div class="chat-header">
        <div class="chat-title-section">
          <button class="mobile-menu-btn" @click="sidebarOpen = !sidebarOpen">
            <i class="fas fa-bars"></i>
          </button>
          <div class="chat-title">
            <h1>{{ currentConversationTitle }}</h1>
            <div class="ai-status">
              <div class="status-indicator" :class="aiStatus"></div>
              <span>{{ getStatusText() }}</span>
            </div>
          </div>
        </div>
        
        <div class="chat-controls">
          <button class="control-btn" @click="showPersonalityPanel = true" :title="$t('personality')">
            <i class="fas fa-user-cog"></i>
          </button>
          <button class="control-btn" @click="showReasoningPanel = !showReasoningPanel" :title="$t('reasoning')">
            <i class="fas fa-brain"></i>
          </button>
          <button class="control-btn" @click="exportConversation" :title="$t('export')">
            <i class="fas fa-download"></i>
          </button>
        </div>
      </div>
      
      <!-- Messages Container -->
      <div class="messages-container" ref="messagesContainer">
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message"
          :class="`${message.role}-message`"
        >
          <div class="message-avatar" :class="`${message.role}-avatar`">
            {{ message.role === 'user' ? getUserInitial() : 'W' }}
          </div>
          
          <div class="message-content">
            <div class="message-header">
              <span class="message-sender">
                {{ message.role === 'user' ? $t('you') : 'WIDDX AI' }}
              </span>
              <span class="message-time">{{ formatTime(message.created_at) }}</span>
              <div v-if="message.confidence_score" class="confidence-badge">
                <i class="fas fa-chart-line"></i>
                {{ Math.round(message.confidence_score * 100) }}%
              </div>
            </div>
            
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            
            <!-- Reasoning Steps -->
            <div v-if="message.reasoning_steps && showReasoningPanel" class="reasoning-steps">
              <h4><i class="fas fa-brain"></i> {{ $t('reasoningSteps') }}</h4>
              <div 
                v-for="(step, index) in message.reasoning_steps" 
                :key="index"
                class="reasoning-step"
                :style="{ animationDelay: `${index * 0.1}s` }"
              >
                <div class="step-header">
                  <span class="step-number">{{ step.step }}</span>
                  <span class="step-type">{{ step.type }}</span>
                </div>
                <div class="step-description">{{ step.description }}</div>
                <div class="step-content">{{ step.content }}</div>
                <div class="step-confidence">
                  {{ $t('confidence') }}: {{ Math.round(step.confidence * 100) }}%
                </div>
              </div>
            </div>
            
            <!-- Tool Executions -->
            <div v-if="message.tool_calls && message.tool_calls.length > 0" class="tool-executions">
              <h4><i class="fas fa-tools"></i> {{ $t('toolsUsed') }}</h4>
              <div 
                v-for="tool in message.tool_calls" 
                :key="tool.tool_name"
                class="tool-execution"
                :class="tool.status"
              >
                <i :class="getToolIcon(tool.status)"></i>
                <span class="tool-name">{{ tool.tool_name }}</span>
                <span class="tool-summary">{{ tool.summary }}</span>
                <span class="tool-time">{{ tool.execution_time }}ms</span>
              </div>
            </div>
            
            <!-- Message Actions -->
            <div v-if="message.role === 'assistant'" class="message-actions">
              <button class="action-btn" @click="copyMessage(message)">
                <i class="fas fa-copy"></i> {{ $t('copy') }}
              </button>
              <button class="action-btn" @click="regenerateResponse(message)">
                <i class="fas fa-redo"></i> {{ $t('regenerate') }}
              </button>
              <button class="action-btn" @click="rateMessage(message, 'up')">
                <i class="fas fa-thumbs-up"></i>
              </button>
              <button class="action-btn" @click="rateMessage(message, 'down')">
                <i class="fas fa-thumbs-down"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="message-avatar ai-avatar">W</div>
          <div class="typing-status">
            <span>{{ $t('aiThinking') }}</span>
            <div class="typing-dots">
              <div class="typing-dot"></div>
              <div class="typing-dot"></div>
              <div class="typing-dot"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Input Area -->
      <div class="input-container">
        <!-- File Preview -->
        <div v-if="attachedFiles.length > 0" class="file-preview">
          <div 
            v-for="(file, index) in attachedFiles" 
            :key="index"
            class="file-item"
          >
            <i :class="getFileIcon(file.type)"></i>
            <span class="file-name">{{ file.name }}</span>
            <button class="file-remove" @click="removeFile(index)">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        
        <div class="input-wrapper">
          <textarea
            ref="messageInput"
            v-model="currentMessage"
            class="message-input"
            :placeholder="$t('messagePlaceholder')"
            @keydown="handleKeyDown"
            @input="autoResize"
            @paste="handlePaste"
            :disabled="isTyping"
            rows="1"
          ></textarea>
          
          <div class="input-actions">
            <button 
              class="input-btn" 
              @click="attachFile" 
              :title="$t('attachFile')"
              :disabled="isTyping"
            >
              <i class="fas fa-paperclip"></i>
            </button>
            
            <button 
              class="input-btn" 
              @click="toggleVoiceInput" 
              :title="$t('voiceInput')"
              :class="{ 'recording': isRecording }"
              :disabled="isTyping"
            >
              <i :class="isRecording ? 'fas fa-stop' : 'fas fa-microphone'"></i>
            </button>
            
            <button 
              class="input-btn send-btn" 
              @click="sendMessage" 
              :title="$t('send')"
              :disabled="!canSend"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Personality Panel -->
    <PersonalityPanel 
      v-if="showPersonalityPanel"
      @close="showPersonalityPanel = false"
      @update="updatePersonality"
    />

    <!-- Settings Modal -->
    <SettingsModal 
      v-if="showSettings"
      @close="showSettings = false"
      @update="updateSettings"
    />

    <!-- Hidden File Input -->
    <input 
      ref="fileInput" 
      type="file" 
      multiple 
      style="display: none"
      @change="handleFileSelect"
      accept=".pdf,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp3,.wav,.csv,.xlsx,.json"
    >
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import PersonalityPanel from './PersonalityPanel.vue'
import SettingsModal from './SettingsModal.vue'
import { useWiddxAI } from '../composables/useWiddxAI'
import { useFileUpload } from '../composables/useFileUpload'
import { useVoiceInput } from '../composables/useVoiceInput'

export default {
  name: 'WiddxAIChat',
  components: {
    PersonalityPanel,
    SettingsModal
  },
  setup() {
    const { t } = useI18n()
    
    // Reactive state
    const isLightMode = ref(localStorage.getItem('theme') === 'light')
    const sidebarOpen = ref(false)
    const showPersonalityPanel = ref(false)
    const showReasoningPanel = ref(true)
    const showSettings = ref(false)
    const currentMessage = ref('')
    const isTyping = ref(false)
    const isRecording = ref(false)
    
    // Use composables
    const {
      conversations,
      messages,
      currentConversationId,
      currentConversationTitle,
      aiStatus,
      loadConversations,
      loadConversation,
      sendMessage: sendAIMessage,
      startNewChat,
      deleteConversation,
      regenerateResponse
    } = useWiddxAI()
    
    const {
      attachedFiles,
      attachFile,
      removeFile,
      handleFileSelect,
      handlePaste
    } = useFileUpload()
    
    const {
      toggleVoiceInput,
      isRecording: voiceRecording
    } = useVoiceInput()
    
    // Computed properties
    const canSend = computed(() => {
      return (currentMessage.value.trim() || attachedFiles.value.length > 0) && !isTyping.value
    })
    
    // Methods
    const toggleTheme = () => {
      isLightMode.value = !isLightMode.value
      localStorage.setItem('theme', isLightMode.value ? 'light' : 'dark')
    }
    
    const sendMessage = async () => {
      if (!canSend.value) return
      
      const message = currentMessage.value.trim()
      const files = [...attachedFiles.value]
      
      // Clear input
      currentMessage.value = ''
      attachedFiles.value = []
      autoResize()
      
      // Send message
      isTyping.value = true
      try {
        await sendAIMessage(message, files)
      } finally {
        isTyping.value = false
      }
    }
    
    const handleKeyDown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
      }
    }
    
    const autoResize = () => {
      nextTick(() => {
        const textarea = messageInput.value
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'
        }
      })
    }
    
    // Initialize
    onMounted(() => {
      loadConversations()
    })
    
    return {
      // Reactive state
      isLightMode,
      sidebarOpen,
      showPersonalityPanel,
      showReasoningPanel,
      showSettings,
      currentMessage,
      isTyping,
      isRecording: voiceRecording,
      
      // Data
      conversations,
      messages,
      currentConversationId,
      currentConversationTitle,
      aiStatus,
      attachedFiles,
      
      // Computed
      canSend,
      
      // Methods
      toggleTheme,
      sendMessage,
      handleKeyDown,
      autoResize,
      loadConversation,
      startNewChat,
      deleteConversation,
      regenerateResponse,
      attachFile,
      removeFile,
      handleFileSelect,
      handlePaste,
      toggleVoiceInput,
      
      // Utils
      t
    }
  }
}
</script>

<style scoped>
@import '../../../css/widdx-ai.css';

.widdx-ai-chat {
  display: flex;
  height: 100vh;
  font-family: 'Inter', 'Cairo', sans-serif;
  background: var(--dark-bg);
  color: var(--dark-text);
  transition: all var(--transition-slow);
}

.widdx-ai-chat.light-mode {
  background: var(--light-bg);
  color: var(--light-text);
}

/* Component-specific styles */
.send-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.send-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
}

.recording {
  background: var(--error-color) !important;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
