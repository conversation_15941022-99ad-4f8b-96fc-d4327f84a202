<template>
  <div class="widdx-ai-app" :class="{ 'rtl': isRTLLanguage, 'dark-mode': isDark }">
    <!-- Sidebar -->
    <div class="sidebar" :class="{ 'open': sidebarOpen }">
      <div class="sidebar-header">
        <div class="logo">
          <i class="fas fa-brain"></i>
        </div>
        <div class="logo-text">{{ $t('app.name') }}</div>
        <button class="theme-toggle" @click="toggleTheme">
          <i :class="isDark ? 'fas fa-sun' : 'fas fa-moon'"></i>
        </button>
      </div>

      <button class="new-chat-btn" @click="createNewChat">
        <i class="fas fa-plus"></i>
        {{ $t('navigation.newChat') }}
      </button>

      <div class="conversations-list">
        <div
          v-for="conversation in conversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ 'active': conversation.id === currentConversationId }"
          @click="selectConversation(conversation.id)"
        >
          <div class="conversation-title">{{ conversation.title }}</div>
          <div class="conversation-preview">{{ getLastMessagePreview(conversation) }}</div>
          <div class="conversation-time">{{ formatTime(conversation.updated_at) }}</div>
        </div>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-main" :class="{ 'sidebar-open': sidebarOpen }">
      <!-- Header -->
      <div class="chat-header">
        <button class="sidebar-toggle" @click="toggleSidebar" v-if="isMobile">
          <i class="fas fa-bars"></i>
        </button>

        <div class="chat-title">
          <h1>{{ $t('chat.title') }}</h1>
          <div class="ai-status">
            <div class="status-indicator"></div>
            <span>{{ $t('status.connected') }}</span>
          </div>
        </div>

        <div class="chat-controls">
          <button class="control-btn" @click="showReasoningSteps = !showReasoningSteps" :class="{ 'active': showReasoningSteps }">
            <i class="fas fa-brain"></i>
          </button>
          <button class="control-btn" @click="toggleLanguage">
            <i class="fas fa-language"></i>
          </button>
          <button class="control-btn" @click="showSettings = true">
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </div>

      <!-- Messages -->
      <div class="messages-container" ref="messagesContainer">
        <div v-if="!hasMessages" class="welcome-message">
          <div class="welcome-content">
            <div class="welcome-icon">
              <i class="fas fa-robot"></i>
            </div>
            <h2>{{ $t('app.tagline') }}</h2>
            <p v-html="$t('messages.welcome')"></p>
          </div>
        </div>

        <div v-for="message in messages" :key="message.id" class="message" :class="message.role">
          <div class="message-avatar">
            <i v-if="message.role === 'user'" class="fas fa-user"></i>
            <i v-else class="fas fa-robot"></i>
          </div>

          <div class="message-content">
            <div class="message-header">
              <span class="message-sender">
                {{ message.role === 'user' ? $t('messages.you') : $t('messages.assistant') }}
              </span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>

            <div class="message-text" v-html="formatMessage(message.content)"></div>

            <!-- Reasoning Steps -->
            <div v-if="message.reasoning_steps && showReasoningSteps" class="reasoning-steps">
              <h4>{{ $t('reasoning.title') }}</h4>
              <div v-for="(step, index) in message.reasoning_steps" :key="index" class="reasoning-step">
                <div class="step-header">
                  <span class="step-number">{{ index + 1 }}</span>
                  <span class="step-title">{{ step.type }}</span>
                  <span class="step-confidence">{{ Math.round(step.confidence * 100) }}%</span>
                </div>
                <div class="step-content">{{ step.content }}</div>
              </div>
            </div>

            <!-- Tool Executions -->
            <div v-if="message.tool_executions" class="tool-executions">
              <h4>{{ $t('tools.title') }}</h4>
              <div v-for="tool in message.tool_executions" :key="tool.id" class="tool-execution">
                <div class="tool-header">
                  <i :class="getToolIcon(tool.tool_name)"></i>
                  <span class="tool-name">{{ $t(`tools.${tool.tool_name}`) }}</span>
                  <span class="tool-status" :class="tool.status">{{ $t(`tools.${tool.status}`) }}</span>
                </div>
                <div v-if="tool.result" class="tool-result">{{ tool.result }}</div>
              </div>
            </div>

            <!-- Message Actions -->
            <div class="message-actions">
              <button class="action-btn" @click="copyMessage(message.content)">
                <i class="fas fa-copy"></i>
                {{ $t('chat.copy') }}
              </button>
              <button v-if="message.role === 'assistant'" class="action-btn" @click="regenerateMessage">
                <i class="fas fa-redo"></i>
                {{ $t('chat.regenerate') }}
              </button>
              <button class="action-btn" @click="rateMessage(message.id, 'like')">
                <i class="fas fa-thumbs-up"></i>
              </button>
              <button class="action-btn" @click="rateMessage(message.id, 'dislike')">
                <i class="fas fa-thumbs-down"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="message-avatar">
            <i class="fas fa-robot"></i>
          </div>
          <div class="typing-content">
            <span>{{ $t('chat.thinking') }}</span>
            <div class="typing-dots">
              <div class="typing-dot"></div>
              <div class="typing-dot"></div>
              <div class="typing-dot"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Input Area -->
      <div class="input-container">
        <div class="input-wrapper">
          <div class="file-uploads" v-if="uploadedFiles.length > 0">
            <div v-for="file in uploadedFiles" :key="file.id" class="uploaded-file">
              <i :class="getFileIcon(file.type)"></i>
              <span class="file-name">{{ file.name }}</span>
              <button class="remove-file" @click="removeFile(file.id)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div class="input-row">
            <textarea
              ref="messageInput"
              v-model="messageText"
              class="message-input"
              :placeholder="$t('chat.placeholder')"
              @keydown="handleKeydown"
              @input="handleInput"
              rows="1"
            ></textarea>

            <div class="input-actions">
              <label class="file-upload-btn">
                <input type="file" multiple @change="handleFileUpload" style="display: none;">
                <i class="fas fa-paperclip"></i>
              </label>

              <button
                class="voice-btn"
                @click="toggleVoiceInput"
                :class="{ 'active': isRecording }"
              >
                <i class="fas fa-microphone"></i>
              </button>

              <button
                class="send-btn"
                @click="sendMessage"
                :disabled="!canSend"
              >
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Overlay -->
    <div v-if="sidebarOpen && isMobile" class="mobile-overlay" @click="toggleSidebar"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { marked } from 'marked'
import { getAvailableLanguages, setLanguage, getCurrentLanguage, isRTL } from '../i18n'

// Composables
const { t, locale } = useI18n()

// Reactive state
const sidebarOpen = ref(true)
const showSettings = ref(false)
const showReasoningSteps = ref(true)
const messageText = ref('')
const isTyping = ref(false)
const isRecording = ref(false)
const currentTheme = ref(localStorage.getItem('widdx-theme') || 'dark')
const selectedLanguage = ref(getCurrentLanguage())
const conversations = ref([])
const messages = ref([])
const currentConversationId = ref(null)
const uploadedFiles = ref([])
const messagesContainer = ref(null)
const messageInput = ref(null)

// Computed
const isDark = computed(() => currentTheme.value === 'dark')
const isRTLLanguage = computed(() => isRTL(selectedLanguage.value))
const isMobile = computed(() => window.innerWidth < 768)
const hasMessages = computed(() => messages.value.length > 0)
const canSend = computed(() => messageText.value.trim().length > 0 || uploadedFiles.value.length > 0)
const availableLanguages = computed(() => getAvailableLanguages())
const themes = computed(() => ['light', 'dark'])

// Methods
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const toggleTheme = () => {
  setTheme(isDark.value ? 'light' : 'dark')
}

const setTheme = (theme) => {
  currentTheme.value = theme
  localStorage.setItem('widdx-theme', theme)
  document.body.classList.remove('light-mode', 'dark-mode')
  document.body.classList.add(`${theme}-mode`)
}

const toggleLanguage = () => {
  const languages = availableLanguages.value
  const currentIndex = languages.findIndex(lang => lang.code === selectedLanguage.value)
  const nextIndex = (currentIndex + 1) % languages.length
  changeLanguage(languages[nextIndex].code)
}

const changeLanguage = (langCode) => {
  selectedLanguage.value = langCode
  setLanguage(langCode)
  locale.value = langCode
}

const createNewChat = async () => {
  try {
    const response = await fetch('/api/v2/conversations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ title: 'New Chat' })
    })

    const data = await response.json()
    const conversation = data.data

    conversations.value.unshift(conversation)
    selectConversation(conversation.id)
  } catch (error) {
    console.error('Failed to create conversation:', error)
  }
}

const selectConversation = async (conversationId) => {
  currentConversationId.value = conversationId

  try {
    const response = await fetch(`/api/v2/conversations/${conversationId}`)
    const data = await response.json()
    messages.value = data.data.messages || []

    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to load conversation:', error)
  }
}

const sendMessage = async () => {
  if (!canSend.value) return

  const content = messageText.value.trim()
  const files = [...uploadedFiles.value]

  // Clear input
  messageText.value = ''
  uploadedFiles.value = []

  // Add user message
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content,
    files,
    timestamp: new Date().toISOString()
  }

  messages.value.push(userMessage)
  isTyping.value = true

  await nextTick()
  scrollToBottom()

  try {
    const formData = new FormData()
    formData.append('message', content)
    if (currentConversationId.value) {
      formData.append('conversation_id', currentConversationId.value)
    }

    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })

    const response = await fetch('/api/v2/chat', {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: formData
    })

    const data = await response.json()

    // Add AI response
    const aiMessage = {
      id: data.data.ai_message.id,
      role: 'assistant',
      content: data.data.ai_message.content,
      reasoning_steps: data.data.reasoning_steps || [],
      tool_executions: data.data.tool_executions || [],
      confidence_score: data.data.confidence_score || 0.9,
      timestamp: data.data.ai_message.timestamp
    }

    messages.value.push(aiMessage)

    // Update conversation if needed
    if (!currentConversationId.value && data.data.conversation_id) {
      currentConversationId.value = data.data.conversation_id
    }

  } catch (error) {
    console.error('Failed to send message:', error)

    // Add error message
    messages.value.push({
      id: Date.now(),
      role: 'system',
      content: t('messages.errorGeneral'),
      error: true,
      timestamp: new Date().toISOString()
    })
  } finally {
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const handleInput = () => {
  // Auto-resize textarea
  const textarea = messageInput.value
  textarea.style.height = 'auto'
  textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'
}

const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    uploadedFiles.value.push({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      type: file.type,
      size: file.size
    })
  })
}

const removeFile = (fileId) => {
  uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== fileId)
}

const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    // Show toast notification
  } catch (error) {
    console.error('Failed to copy message:', error)
  }
}

const regenerateMessage = () => {
  if (messages.value.length >= 2) {
    const lastUserMessage = messages.value[messages.value.length - 2]
    messages.value.pop() // Remove AI message
    messageText.value = lastUserMessage.content
    sendMessage()
  }
}

const rateMessage = async (messageId, rating) => {
  try {
    await fetch(`/api/v2/messages/${messageId}/rate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ rating })
    })
  } catch (error) {
    console.error('Failed to rate message:', error)
  }
}

const formatMessage = (content) => {
  return marked(content)
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getLastMessagePreview = (conversation) => {
  return conversation.last_message || 'No messages yet'
}

const getToolIcon = (toolName) => {
  const icons = {
    calculator: 'fas fa-calculator',
    webSearch: 'fas fa-search',
    codeAnalyzer: 'fas fa-code',
    dataAnalyzer: 'fas fa-chart-bar',
    summarizer: 'fas fa-compress-alt',
    translator: 'fas fa-language',
    imageAnalyzer: 'fas fa-image'
  }
  return icons[toolName] || 'fas fa-tool'
}

const getFileIcon = (fileType) => {
  if (fileType.startsWith('image/')) return 'fas fa-image'
  if (fileType.startsWith('video/')) return 'fas fa-video'
  if (fileType.startsWith('audio/')) return 'fas fa-music'
  if (fileType.includes('pdf')) return 'fas fa-file-pdf'
  if (fileType.includes('word')) return 'fas fa-file-word'
  if (fileType.includes('excel')) return 'fas fa-file-excel'
  return 'fas fa-file'
}

const toggleVoiceInput = () => {
  isRecording.value = !isRecording.value
  // Implement voice recording logic
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize theme
  setTheme(currentTheme.value)

  // Load conversations
  try {
    const response = await fetch('/api/v2/conversations')
    const data = await response.json()
    conversations.value = data.data || []

    // Load first conversation or create new one
    if (conversations.value.length > 0) {
      selectConversation(conversations.value[0].id)
    } else {
      await createNewChat()

      // Add welcome message
      messages.value.push({
        id: 'welcome',
        role: 'assistant',
        content: t('messages.welcome'),
        timestamp: new Date().toISOString(),
        isWelcome: true
      })
    }
  } catch (error) {
    console.error('Failed to load conversations:', error)
  }
})

// Watch for theme changes
watch(currentTheme, (newTheme) => {
  setTheme(newTheme)
})

// Watch for language changes
watch(selectedLanguage, (newLang) => {
  changeLanguage(newLang)
})
</script>
