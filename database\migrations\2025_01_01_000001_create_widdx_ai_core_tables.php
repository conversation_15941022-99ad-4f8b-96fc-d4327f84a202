<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // 1. Users and Personalities
        Schema::create('ai_personalities', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description');
            $table->json('traits'); // voice, tone, behavior patterns
            $table->json('preferences'); // response style, verbosity, etc.
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('user_personalities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ai_personality_id')->constrained()->onDelete('cascade');
            $table->json('customizations')->nullable(); // user-specific overrides
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['user_id', 'ai_personality_id']);
        });

        // 2. Enhanced Conversations
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('ai_personality_id')->constrained();
            $table->string('title')->nullable();
            $table->json('context')->nullable(); // conversation context and metadata
            $table->json('memory_tags')->nullable(); // important topics, entities
            $table->enum('status', ['active', 'archived', 'deleted'])->default('active');
            $table->timestamp('last_activity_at');
            $table->timestamps();
            
            $table->index(['user_id', 'status', 'last_activity_at']);
        });

        // 3. Enhanced Messages with Multi-Modal Support
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['user', 'assistant', 'system']);
            $table->text('content');
            $table->json('metadata')->nullable(); // model used, tokens, confidence, etc.
            $table->json('attachments')->nullable(); // file references
            $table->json('reasoning_steps')->nullable(); // chain of thought
            $table->json('tool_calls')->nullable(); // internal tools used
            $table->float('confidence_score')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            
            $table->index(['conversation_id', 'created_at']);
        });

        // 4. Knowledge Base with Embeddings
        Schema::create('knowledge_entries', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->text('summary')->nullable();
            $table->json('tags')->nullable(); // topic, category, source
            $table->string('source_type'); // user_qa, document, web, manual
            $table->string('source_id')->nullable(); // reference to original source
            $table->foreignId('created_by_user_id')->nullable()->constrained('users');
            $table->string('model_used')->nullable();
            $table->float('confidence_score')->nullable();
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();
            
            $table->fullText(['title', 'content', 'summary']);
            $table->index(['source_type', 'source_id']);
        });

        // 5. Vector Embeddings Storage
        Schema::create('embeddings', function (Blueprint $table) {
            $table->id();
            $table->morphs('embeddable'); // knowledge_entries, messages, etc.
            $table->string('model_name'); // text-embedding-ada-002, etc.
            $table->json('vector'); // embedding vector
            $table->integer('dimensions');
            $table->timestamps();
            
            $table->index(['embeddable_type', 'embeddable_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('embeddings');
        Schema::dropIfExists('knowledge_entries');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('conversations');
        Schema::dropIfExists('user_personalities');
        Schema::dropIfExists('ai_personalities');
    }
};
