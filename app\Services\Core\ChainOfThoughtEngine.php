<?php

namespace App\Services\Core;

use App\Models\AiTool;
use App\Models\ToolExecution;
use App\Models\Message;
use App\Services\Tools\ToolInterface;
use Illuminate\Support\Facades\Log;

/**
 * Chain of Thought + Internal Tools Engine
 * Provides step-by-step reasoning and tool execution capabilities
 */
class ChainOfThoughtEngine
{
    private array $availableTools = [];
    private int $maxReasoningSteps = 10;
    private float $confidenceThreshold = 0.6;

    public function __construct()
    {
        $this->loadAvailableTools();
    }

    /**
     * Process request with chain of thought reasoning
     */
    public function processWithReasoning(string $message, array $context = []): array
    {
        $reasoningChain = [];
        $toolExecutions = [];
        $finalResponse = '';

        // Step 1: Initial analysis
        $analysis = $this->analyzeRequest($message, $context);
        $reasoningChain[] = [
            'step' => 1,
            'type' => 'analysis',
            'description' => 'Analyzing the user request',
            'content' => $analysis,
            'confidence' => 0.9
        ];

        // Step 2: Determine if tools are needed
        $requiredTools = $this->identifyRequiredTools($message, $analysis);
        if (!empty($requiredTools)) {
            $reasoningChain[] = [
                'step' => 2,
                'type' => 'tool_planning',
                'description' => 'Identifying required tools',
                'content' => "I need to use these tools: " . implode(', ', array_keys($requiredTools)),
                'confidence' => 0.8
            ];

            // Execute tools
            foreach ($requiredTools as $toolName => $parameters) {
                $toolResult = $this->executeTool($toolName, $parameters);
                $toolExecutions[] = $toolResult;
                
                $reasoningChain[] = [
                    'step' => count($reasoningChain) + 1,
                    'type' => 'tool_execution',
                    'description' => "Executing {$toolName}",
                    'content' => $toolResult['summary'] ?? 'Tool executed successfully',
                    'confidence' => $toolResult['confidence'] ?? 0.7
                ];
            }
        }

        // Step 3: Synthesize information
        $synthesis = $this->synthesizeInformation($message, $analysis, $toolExecutions);
        $reasoningChain[] = [
            'step' => count($reasoningChain) + 1,
            'type' => 'synthesis',
            'description' => 'Synthesizing information and forming response',
            'content' => $synthesis['reasoning'],
            'confidence' => $synthesis['confidence']
        ];

        // Step 4: Generate final response
        $finalResponse = $this->generateFinalResponse($message, $reasoningChain, $toolExecutions);
        $reasoningChain[] = [
            'step' => count($reasoningChain) + 1,
            'type' => 'conclusion',
            'description' => 'Final response generated',
            'content' => 'Response completed with confidence: ' . $this->calculateOverallConfidence($reasoningChain),
            'confidence' => $this->calculateOverallConfidence($reasoningChain)
        ];

        return [
            'response' => $finalResponse,
            'reasoning_chain' => $reasoningChain,
            'tool_executions' => $toolExecutions,
            'metadata' => [
                'reasoning_steps' => count($reasoningChain),
                'tools_used' => array_keys($requiredTools),
                'overall_confidence' => $this->calculateOverallConfidence($reasoningChain)
            ]
        ];
    }

    /**
     * Analyze the user request to understand intent and complexity
     */
    private function analyzeRequest(string $message, array $context): array
    {
        return [
            'intent' => $this->detectIntent($message),
            'complexity' => $this->assessComplexity($message),
            'domain' => $this->identifyDomain($message),
            'requires_calculation' => $this->requiresCalculation($message),
            'requires_search' => $this->requiresSearch($message),
            'requires_analysis' => $this->requiresAnalysis($message),
            'language' => $this->detectLanguage($message),
            'context_relevance' => $this->assessContextRelevance($context)
        ];
    }

    /**
     * Identify which tools are needed for this request
     */
    private function identifyRequiredTools(string $message, array $analysis): array
    {
        $requiredTools = [];

        // Calculator tool
        if ($analysis['requires_calculation'] || preg_match('/\d+[\+\-\*\/]\d+/', $message)) {
            $requiredTools['calculator'] = $this->extractCalculationParameters($message);
        }

        // Search tool
        if ($analysis['requires_search'] || preg_match('/\b(search|find|lookup|بحث|ابحث)\b/i', $message)) {
            $requiredTools['web_search'] = $this->extractSearchParameters($message);
        }

        // Code analyzer tool
        if (preg_match('/```|function|class|def /', $message)) {
            $requiredTools['code_analyzer'] = $this->extractCodeParameters($message);
        }

        // Data analyzer tool
        if ($analysis['requires_analysis'] || preg_match('/\b(analyze|data|statistics|تحليل)\b/i', $message)) {
            $requiredTools['data_analyzer'] = $this->extractAnalysisParameters($message);
        }

        // Summarizer tool
        if (preg_match('/\b(summarize|summary|tldr|ملخص)\b/i', $message)) {
            $requiredTools['summarizer'] = $this->extractSummaryParameters($message);
        }

        return $requiredTools;
    }

    /**
     * Execute a specific tool
     */
    private function executeTool(string $toolName, array $parameters): array
    {
        $startTime = microtime(true);
        
        try {
            if (!isset($this->availableTools[$toolName])) {
                throw new \Exception("Tool {$toolName} not available");
            }

            $tool = $this->availableTools[$toolName];
            $result = $tool->execute($parameters);
            $executionTime = microtime(true) - $startTime;

            // Log tool execution
            Log::info("Tool executed successfully", [
                'tool' => $toolName,
                'execution_time' => $executionTime,
                'parameters' => $parameters
            ]);

            return [
                'tool_name' => $toolName,
                'status' => 'success',
                'result' => $result,
                'execution_time' => $executionTime,
                'confidence' => $result['confidence'] ?? 0.8,
                'summary' => $result['summary'] ?? "Successfully executed {$toolName}"
            ];

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            Log::error("Tool execution failed", [
                'tool' => $toolName,
                'error' => $e->getMessage(),
                'parameters' => $parameters
            ]);

            return [
                'tool_name' => $toolName,
                'status' => 'failed',
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
                'confidence' => 0.0,
                'summary' => "Failed to execute {$toolName}: " . $e->getMessage()
            ];
        }
    }

    /**
     * Synthesize information from analysis and tool results
     */
    private function synthesizeInformation(string $message, array $analysis, array $toolExecutions): array
    {
        $insights = [];
        $confidence = 0.7; // Base confidence

        // Analyze tool results
        foreach ($toolExecutions as $execution) {
            if ($execution['status'] === 'success') {
                $insights[] = $execution['result'];
                $confidence += 0.1; // Increase confidence for successful tool execution
            } else {
                $confidence -= 0.2; // Decrease confidence for failed tools
            }
        }

        // Combine insights with original analysis
        $reasoning = $this->generateReasoningText($analysis, $insights);

        return [
            'reasoning' => $reasoning,
            'insights' => $insights,
            'confidence' => max(0.1, min(1.0, $confidence))
        ];
    }

    /**
     * Generate the final response based on reasoning chain
     */
    private function generateFinalResponse(string $message, array $reasoningChain, array $toolExecutions): string
    {
        $response = "Let me think through this step by step:\n\n";

        // Add reasoning steps
        foreach ($reasoningChain as $step) {
            if ($step['type'] !== 'conclusion') {
                $response .= "**Step {$step['step']}:** {$step['description']}\n";
                $response .= "{$step['content']}\n\n";
            }
        }

        // Add tool results if any
        if (!empty($toolExecutions)) {
            $response .= "**Tool Results:**\n";
            foreach ($toolExecutions as $execution) {
                if ($execution['status'] === 'success') {
                    $response .= "- {$execution['tool_name']}: {$execution['summary']}\n";
                }
            }
            $response .= "\n";
        }

        // Add final conclusion
        $response .= "**Conclusion:**\n";
        $response .= $this->generateConclusion($message, $reasoningChain, $toolExecutions);

        return $response;
    }

    // Helper methods
    private function loadAvailableTools(): void
    {
        $tools = AiTool::where('is_active', true)->get();
        
        foreach ($tools as $tool) {
            try {
                $toolClass = $tool->handler_class;
                if (class_exists($toolClass)) {
                    $this->availableTools[$tool->name] = new $toolClass();
                }
            } catch (\Exception $e) {
                Log::warning("Failed to load tool: {$tool->name}", ['error' => $e->getMessage()]);
            }
        }
    }

    private function detectIntent(string $message): string
    {
        if (preg_match('/\b(calculate|compute|math|حساب|احسب)\b/i', $message)) {
            return 'calculation';
        } elseif (preg_match('/\b(explain|describe|what|how|اشرح|ما|كيف)\b/i', $message)) {
            return 'explanation';
        } elseif (preg_match('/\b(analyze|review|check|تحليل|راجع)\b/i', $message)) {
            return 'analysis';
        } elseif (preg_match('/\b(create|generate|make|أنشئ|اصنع)\b/i', $message)) {
            return 'creation';
        }
        
        return 'general_inquiry';
    }

    private function assessComplexity(string $message): string
    {
        $wordCount = str_word_count($message);
        $hasCode = preg_match('/```|function|class/', $message);
        $hasMultipleQuestions = substr_count($message, '?') > 1;
        
        if ($wordCount > 100 || $hasCode || $hasMultipleQuestions) {
            return 'high';
        } elseif ($wordCount > 30) {
            return 'medium';
        }
        
        return 'low';
    }

    private function identifyDomain(string $message): string
    {
        $domains = [
            'programming' => ['code', 'function', 'class', 'programming', 'برمجة', 'كود'],
            'mathematics' => ['calculate', 'equation', 'formula', 'math', 'رياضيات', 'حساب'],
            'data_science' => ['data', 'analysis', 'statistics', 'بيانات', 'تحليل'],
            'web_development' => ['html', 'css', 'javascript', 'website', 'موقع'],
            'general' => []
        ];

        foreach ($domains as $domain => $keywords) {
            foreach ($keywords as $keyword) {
                if (stripos($message, $keyword) !== false) {
                    return $domain;
                }
            }
        }

        return 'general';
    }

    private function requiresCalculation(string $message): bool
    {
        return preg_match('/\d+[\+\-\*\/]\d+|\b(calculate|compute|sum|total|حساب|احسب)\b/i', $message);
    }

    private function requiresSearch(string $message): bool
    {
        return preg_match('/\b(search|find|lookup|latest|current|بحث|ابحث|أحدث)\b/i', $message);
    }

    private function requiresAnalysis(string $message): bool
    {
        return preg_match('/\b(analyze|review|compare|evaluate|تحليل|قارن|قيم)\b/i', $message);
    }

    private function detectLanguage(string $message): string
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $message) ? 'ar' : 'en';
    }

    private function assessContextRelevance(array $context): float
    {
        return empty($context) ? 0.0 : 0.5; // Simplified relevance assessment
    }

    private function calculateOverallConfidence(array $reasoningChain): float
    {
        if (empty($reasoningChain)) return 0.5;
        
        $totalConfidence = array_sum(array_column($reasoningChain, 'confidence'));
        return $totalConfidence / count($reasoningChain);
    }

    private function extractCalculationParameters(string $message): array
    {
        preg_match_all('/\d+[\+\-\*\/]\d+/', $message, $matches);
        return ['expressions' => $matches[0] ?? []];
    }

    private function extractSearchParameters(string $message): array
    {
        // Extract search terms (simplified)
        return ['query' => $message, 'limit' => 5];
    }

    private function extractCodeParameters(string $message): array
    {
        preg_match('/```(\w+)?\n(.*?)```/s', $message, $matches);
        return [
            'language' => $matches[1] ?? 'unknown',
            'code' => $matches[2] ?? $message
        ];
    }

    private function extractAnalysisParameters(string $message): array
    {
        return ['text' => $message, 'type' => 'general'];
    }

    private function extractSummaryParameters(string $message): array
    {
        return ['text' => $message, 'max_length' => 200];
    }

    private function generateReasoningText(array $analysis, array $insights): string
    {
        $reasoning = "Based on my analysis, this is a {$analysis['complexity']} complexity {$analysis['intent']} in the {$analysis['domain']} domain. ";
        
        if (!empty($insights)) {
            $reasoning .= "The tools provided additional insights that help me understand the context better. ";
        }
        
        return $reasoning;
    }

    private function generateConclusion(string $message, array $reasoningChain, array $toolExecutions): string
    {
        return "Based on my step-by-step analysis and the tools I used, I can provide you with a comprehensive response to your question.";
    }
}
