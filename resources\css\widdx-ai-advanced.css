/* WIDDX AI v2.0 - Advanced Multi-language Interface */

:root {
  /* Color System */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  --secondary-50: #faf5ff;
  --secondary-100: #f3e8ff;
  --secondary-200: #e9d5ff;
  --secondary-300: #d8b4fe;
  --secondary-400: #c084fc;
  --secondary-500: #a855f7;
  --secondary-600: #9333ea;
  --secondary-700: #7c3aed;
  --secondary-800: #6b21a8;
  --secondary-900: #581c87;
  
  --accent-50: #ecfeff;
  --accent-100: #cffafe;
  --accent-200: #a5f3fc;
  --accent-300: #67e8f9;
  --accent-400: #22d3ee;
  --accent-500: #06b6d4;
  --accent-600: #0891b2;
  --accent-700: #0e7490;
  --accent-800: #155e75;
  --accent-900: #164e63;
  
  /* Semantic Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  /* Dark Theme Colors */
  --dark-bg-primary: #0f172a;
  --dark-bg-secondary: #1e293b;
  --dark-bg-tertiary: #334155;
  --dark-border: #475569;
  --dark-text-primary: #f1f5f9;
  --dark-text-secondary: #cbd5e1;
  --dark-text-tertiary: #94a3b8;
  
  /* Light Theme Colors */
  --light-bg-primary: #ffffff;
  --light-bg-secondary: #f8fafc;
  --light-bg-tertiary: #f1f5f9;
  --light-border: #e2e8f0;
  --light-text-primary: #1e293b;
  --light-text-secondary: #475569;
  --light-text-tertiary: #64748b;
  
  /* Typography */
  --font-family-sans: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* Dark Mode */
body.dark-mode {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

body.dark-mode .widdx-ai-app {
  --bg-primary: var(--dark-bg-primary);
  --bg-secondary: var(--dark-bg-secondary);
  --bg-tertiary: var(--dark-bg-tertiary);
  --border-color: var(--dark-border);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --text-tertiary: var(--dark-text-tertiary);
}

/* Light Mode */
body.light-mode {
  background-color: var(--light-bg-primary);
  color: var(--light-text-primary);
}

body.light-mode .widdx-ai-app {
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --bg-tertiary: var(--light-bg-tertiary);
  --border-color: var(--light-border);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --text-tertiary: var(--light-text-tertiary);
}

/* RTL Support */
body.rtl {
  direction: rtl;
}

body.rtl .widdx-ai-app {
  direction: rtl;
}

body.rtl .chat-main {
  direction: ltr; /* Keep chat content LTR for better code/content readability */
}

/* Main App Container */
.widdx-ai-app {
  display: flex;
  height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-family-sans);
  transition: all var(--transition-normal);
  position: relative;
}

/* Sidebar Styles */
.sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  z-index: var(--z-fixed);
  position: relative;
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: var(--bg-secondary);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-xl);
  font-weight: 700;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.logo:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.logo-text {
  font-size: var(--text-xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  flex: 1;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
}

.theme-toggle:hover {
  background: var(--primary-500);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.new-chat-btn {
  margin: var(--space-4) var(--space-6);
  padding: var(--space-4) var(--space-5);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-sm);
}

.new-chat-btn:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.new-chat-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 var(--space-4);
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.conversations-list::-webkit-scrollbar {
  width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
  background: transparent;
}

.conversations-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-full);
}

.conversations-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

.conversation-item {
  padding: var(--space-4);
  margin-bottom: var(--space-2);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.conversation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: -1;
}

.conversation-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(4px);
}

.conversation-item.active {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-900);
}

body.dark-mode .conversation-item.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: var(--primary-300);
}

.conversation-item.active::before {
  opacity: 0.05;
}

.conversation-title {
  font-weight: 600;
  font-size: var(--text-sm);
  margin-bottom: var(--space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.conversation-preview {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: var(--space-1);
}

.conversation-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: 500;
}

/* Main Chat Area */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: var(--bg-primary);
  transition: all var(--transition-normal);
}

.chat-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: none;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-4);
}

.sidebar-toggle:hover {
  background: var(--primary-500);
  color: white;
}

.chat-title h1 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.ai-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--success-500);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.control-btn {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  position: relative;
}

.control-btn:hover {
  background: var(--primary-500);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.control-btn.active {
  background: var(--primary-500);
  color: white;
  box-shadow: var(--shadow-md);
}

.control-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-full);
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 600px;
  padding: var(--space-8);
}

.welcome-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  color: white;
  font-size: var(--text-3xl);
  box-shadow: var(--shadow-xl);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.welcome-content h2 {
  font-size: var(--text-3xl);
  font-weight: 800;
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.8;
}

/* Message Styles */
.message {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  animation: fadeInUp 0.4s ease-out;
  position: relative;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
  font-weight: 600;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, var(--accent-500), var(--accent-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.message.assistant .message-avatar {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  color: white;
  box-shadow: var(--shadow-md);
}

.message.system .message-avatar {
  background: var(--warning-500);
  color: white;
}

.message-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.message:hover .message-avatar::before {
  transform: translateX(100%);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.message-sender {
  font-weight: 600;
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.message-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: 500;
}

.message-text {
  background: var(--bg-secondary);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-xl);
  line-height: 1.7;
  word-wrap: break-word;
  position: relative;
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.message.user .message-text {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border-color: var(--primary-400);
  margin-left: auto;
  max-width: 85%;
  box-shadow: var(--shadow-md);
}

.message.assistant .message-text {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
}

.message.assistant .message-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  opacity: 0.6;
}

.message.system .message-text {
  background: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

body.dark-mode .message.system .message-text {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: var(--warning-300);
}

/* Message Text Content Styling */
.message-text h1, .message-text h2, .message-text h3, 
.message-text h4, .message-text h5, .message-text h6 {
  margin: var(--space-4) 0 var(--space-2);
  font-weight: 700;
}

.message-text h1 { font-size: var(--text-2xl); }
.message-text h2 { font-size: var(--text-xl); }
.message-text h3 { font-size: var(--text-lg); }

.message-text p {
  margin: var(--space-2) 0;
}

.message-text ul, .message-text ol {
  margin: var(--space-2) 0;
  padding-left: var(--space-6);
}

.message-text li {
  margin: var(--space-1) 0;
}

.message-text code {
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  border: 1px solid var(--border-color);
}

.message-text pre {
  background: var(--bg-tertiary);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin: var(--space-4) 0;
  border: 1px solid var(--border-color);
}

.message-text pre code {
  background: none;
  padding: 0;
  border: none;
  font-size: var(--text-sm);
}

.message-text blockquote {
  border-left: 4px solid var(--primary-500);
  padding-left: var(--space-4);
  margin: var(--space-4) 0;
  font-style: italic;
  color: var(--text-secondary);
}

.message-text a {
  color: var(--primary-500);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.message-text a:hover {
  color: var(--primary-600);
  text-decoration: underline;
}

/* Reasoning Steps */
.reasoning-steps {
  margin-top: var(--space-5);
  padding: var(--space-5);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-xl);
  border-left: 4px solid var(--primary-500);
  position: relative;
  overflow: hidden;
}

.reasoning-steps::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-500), transparent);
}

.reasoning-steps h4 {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

body.dark-mode .reasoning-steps h4 {
  color: var(--primary-400);
}

.reasoning-steps h4::before {
  content: '🧠';
  font-size: var(--text-base);
}

.reasoning-step {
  margin-bottom: var(--space-4);
  padding: var(--space-3) 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  animation: fadeInStep 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

.reasoning-step:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.reasoning-step:nth-child(2) { animation-delay: 0.1s; }
.reasoning-step:nth-child(3) { animation-delay: 0.2s; }
.reasoning-step:nth-child(4) { animation-delay: 0.3s; }
.reasoning-step:nth-child(5) { animation-delay: 0.4s; }
.reasoning-step:nth-child(6) { animation-delay: 0.5s; }

@keyframes fadeInStep {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.step-number {
  width: 24px;
  height: 24px;
  background: var(--primary-500);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: 700;
  flex-shrink: 0;
}

.step-title {
  font-weight: 600;
  color: var(--text-primary);
  text-transform: capitalize;
}

.step-confidence {
  margin-left: auto;
  padding: var(--space-1) var(--space-2);
  background: var(--success-100);
  color: var(--success-700);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

body.dark-mode .step-confidence {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-400);
}

.step-content {
  color: var(--text-secondary);
  line-height: 1.6;
  padding-left: var(--space-8);
}

/* Tool Executions */
.tool-executions {
  margin-top: var(--space-5);
  padding: var(--space-5);
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05), rgba(34, 197, 94, 0.05));
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: var(--radius-xl);
  border-left: 4px solid var(--accent-500);
}

.tool-executions h4 {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--accent-600);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

body.dark-mode .tool-executions h4 {
  color: var(--accent-400);
}

.tool-executions h4::before {
  content: '🛠️';
  font-size: var(--text-base);
}

.tool-execution {
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.tool-execution:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.tool-execution:last-child {
  margin-bottom: 0;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.tool-header i {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-500);
  font-size: var(--text-sm);
}

.tool-name {
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.tool-status {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tool-status.success {
  background: var(--success-100);
  color: var(--success-700);
}

.tool-status.failed {
  background: var(--error-100);
  color: var(--error-700);
}

.tool-status.running {
  background: var(--warning-100);
  color: var(--warning-700);
  animation: pulse 1.5s infinite;
}

.tool-status.pending {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
}

body.dark-mode .tool-status.success {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-400);
}

body.dark-mode .tool-status.failed {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error-400);
}

body.dark-mode .tool-status.running {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-400);
}

.tool-result {
  background: var(--bg-secondary);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Message Actions */
.message-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-3);
  opacity: 0;
  transition: opacity var(--transition-normal);
  flex-wrap: wrap;
}

.message:hover .message-actions {
  opacity: 1;
}

.action-btn {
  padding: var(--space-2) var(--space-3);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: 500;
}

.action-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.action-btn i {
  font-size: var(--text-xs);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  animation: fadeInUp 0.4s ease-out;
}

.typing-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-5);
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-style: italic;
}

.typing-dots {
  display: flex;
  gap: var(--space-1);
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
  background: var(--primary-500);
  animation: typingDot 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

/* Input Container */
.input-container {
  padding: var(--space-6);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
}

.input-wrapper {
  max-width: 1000px;
  margin: 0 auto;
}

.file-uploads {
  margin-bottom: var(--space-4);
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.uploaded-file i {
  color: var(--primary-500);
}

.file-name {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-file {
  width: 20px;
  height: 20px;
  background: var(--error-500);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
}

.remove-file:hover {
  background: var(--error-600);
  transform: scale(1.1);
}

.input-row {
  display: flex;
  align-items: end;
  gap: var(--space-3);
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-2xl);
  padding: var(--space-3);
  transition: all var(--transition-normal);
  position: relative;
}

.input-row:focus-within {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 200px;
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: var(--text-base);
  font-family: var(--font-family-sans);
  resize: none;
  outline: none;
  line-height: 1.5;
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

.input-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.file-upload-btn,
.voice-btn,
.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
  position: relative;
  overflow: hidden;
}

.file-upload-btn,
.voice-btn {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.file-upload-btn:hover,
.voice-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.voice-btn.active {
  background: var(--error-500);
  color: white;
  border-color: var(--error-500);
  animation: pulse 1.5s infinite;
}

.send-btn {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.send-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: var(--z-modal-backdrop);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 280px;
  }
  
  .messages-container {
    padding: var(--space-4);
  }
  
  .input-container {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -320px;
    height: 100vh;
    z-index: var(--z-modal);
    box-shadow: var(--shadow-2xl);
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .chat-main {
    width: 100%;
  }
  
  .sidebar-toggle {
    display: flex;
  }
  
  .chat-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .chat-title h1 {
    font-size: var(--text-lg);
  }
  
  .messages-container {
    padding: var(--space-3);
  }
  
  .message {
    gap: var(--space-3);
  }
  
  .message-avatar {
    width: 36px;
    height: 36px;
    font-size: var(--text-sm);
  }
  
  .message.user .message-text {
    max-width: 90%;
  }
  
  .input-container {
    padding: var(--space-3);
  }
  
  .input-row {
    padding: var(--space-2);
  }
  
  .file-upload-btn,
  .voice-btn,
  .send-btn {
    width: 40px;
    height: 40px;
    font-size: var(--text-sm);
  }
  
  .reasoning-steps,
  .tool-executions {
    padding: var(--space-4);
    margin-top: var(--space-4);
  }
  
  .step-content {
    padding-left: var(--space-6);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
    left: -100vw;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .chat-header {
    padding: var(--space-2) var(--space-3);
  }
  
  .chat-controls {
    gap: var(--space-1);
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
    font-size: var(--text-xs);
  }
  
  .messages-container {
    padding: var(--space-2);
  }
  
  .message {
    gap: var(--space-2);
    margin-bottom: var(--space-4);
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--text-xs);
  }
  
  .message-text {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }
  
  .input-container {
    padding: var(--space-2);
  }
  
  .file-upload-btn,
  .voice-btn,
  .send-btn {
    width: 36px;
    height: 36px;
    font-size: var(--text-xs);
  }
  
  .message-input {
    font-size: var(--text-sm);
    padding: var(--space-2) var(--space-3);
  }
  
  .action-btn {
    padding: var(--space-1) var(--space-2);
    font-size: 10px;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .chat-header,
  .input-container,
  .message-actions,
  .mobile-overlay {
    display: none !important;
  }
  
  .chat-main {
    width: 100% !important;
  }
  
  .messages-container {
    overflow: visible !important;
    height: auto !important;
  }
  
  .message {
    break-inside: avoid;
    margin-bottom: var(--space-4);
  }
  
  .message-text {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000;
    --text-secondary: #000;
    --text-tertiary: #333;
  }
  
  body.light-mode {
    --bg-primary: #fff;
    --bg-secondary: #f5f5f5;
    --text-primary: #000;
  }
  
  body.dark-mode {
    --bg-primary: #000;
    --bg-secondary: #1a1a1a;
    --text-primary: #fff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .status-indicator,
  .typing-dot,
  .welcome-icon {
    animation: none !important;
  }
}

/* Focus Styles for Accessibility */
.control-btn:focus,
.action-btn:focus,
.send-btn:focus,
.file-upload-btn:focus,
.voice-btn:focus,
.theme-toggle:focus,
.new-chat-btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.message-input:focus {
  outline: none; /* Handled by input-row focus-within */
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
