import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useChatStore = defineStore('chat', () => {
  // State
  const conversations = ref([])
  const currentConversation = ref(null)
  const messages = ref([])
  const isTyping = ref(false)
  const isThinking = ref(false)
  const currentReasoningSteps = ref([])
  const currentToolExecutions = ref([])
  const uploadedFiles = ref([])
  
  // Computed
  const hasMessages = computed(() => messages.value.length > 0)
  const lastMessage = computed(() => messages.value[messages.value.length - 1])
  const conversationTitle = computed(() => {
    if (!currentConversation.value) return 'New Chat'
    return currentConversation.value.title || 'New Chat'
  })
  
  // Actions
  const createNewConversation = async () => {
    try {
      const response = await axios.post('/api/v2/conversations', {
        title: 'New Chat'
      })
      
      const conversation = response.data.data
      conversations.value.unshift(conversation)
      currentConversation.value = conversation
      messages.value = []
      currentReasoningSteps.value = []
      currentToolExecutions.value = []
      
      return conversation
    } catch (error) {
      console.error('Failed to create conversation:', error)
      throw error
    }
  }
  
  const loadConversations = async () => {
    try {
      const response = await axios.get('/api/v2/conversations')
      conversations.value = response.data.data
    } catch (error) {
      console.error('Failed to load conversations:', error)
    }
  }
  
  const loadConversation = async (conversationId) => {
    try {
      const response = await axios.get(`/api/v2/conversations/${conversationId}`)
      currentConversation.value = response.data.data
      messages.value = response.data.data.messages || []
    } catch (error) {
      console.error('Failed to load conversation:', error)
    }
  }
  
  const sendMessage = async (content, files = []) => {
    if (!currentConversation.value) {
      await createNewConversation()
    }
    
    // Add user message
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content,
      files,
      timestamp: new Date().toISOString(),
      temp: true
    }
    
    messages.value.push(userMessage)
    isTyping.value = true
    isThinking.value = true
    
    try {
      const formData = new FormData()
      formData.append('message', content)
      formData.append('conversation_id', currentConversation.value.id)
      
      // Add files if any
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file)
      })
      
      const response = await axios.post('/api/v2/chat', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          // Handle upload progress if needed
        }
      })
      
      // Remove temp user message and add real messages
      messages.value = messages.value.filter(m => !m.temp)
      
      const responseData = response.data.data
      
      // Add user message (confirmed)
      messages.value.push({
        ...userMessage,
        id: responseData.user_message.id,
        temp: false
      })
      
      // Add AI response
      messages.value.push({
        id: responseData.ai_message.id,
        role: 'assistant',
        content: responseData.ai_message.content,
        reasoning_steps: responseData.reasoning_steps || [],
        tool_executions: responseData.tool_executions || [],
        confidence_score: responseData.confidence_score || 0.9,
        timestamp: responseData.ai_message.timestamp
      })
      
      // Update conversation title if it's the first message
      if (messages.value.length === 2 && responseData.conversation_title) {
        currentConversation.value.title = responseData.conversation_title
        const convIndex = conversations.value.findIndex(c => c.id === currentConversation.value.id)
        if (convIndex !== -1) {
          conversations.value[convIndex].title = responseData.conversation_title
        }
      }
      
    } catch (error) {
      // Remove temp message on error
      messages.value = messages.value.filter(m => !m.temp)
      
      // Add error message
      messages.value.push({
        id: Date.now(),
        role: 'system',
        content: 'Sorry, an error occurred while processing your message. Please try again.',
        error: true,
        timestamp: new Date().toISOString()
      })
      
      console.error('Failed to send message:', error)
      throw error
    } finally {
      isTyping.value = false
      isThinking.value = false
    }
  }
  
  const regenerateLastMessage = async () => {
    if (messages.value.length < 2) return
    
    const lastUserMessage = messages.value[messages.value.length - 2]
    
    // Remove last AI message
    messages.value.pop()
    
    // Resend the last user message
    await sendMessage(lastUserMessage.content, lastUserMessage.files || [])
  }
  
  const deleteMessage = async (messageId) => {
    try {
      await axios.delete(`/api/v2/messages/${messageId}`)
      messages.value = messages.value.filter(m => m.id !== messageId)
    } catch (error) {
      console.error('Failed to delete message:', error)
    }
  }
  
  const deleteConversation = async (conversationId) => {
    try {
      await axios.delete(`/api/v2/conversations/${conversationId}`)
      conversations.value = conversations.value.filter(c => c.id !== conversationId)
      
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
        messages.value = []
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error)
    }
  }
  
  const rateMessage = async (messageId, rating) => {
    try {
      await axios.post(`/api/v2/messages/${messageId}/rate`, { rating })
      
      const message = messages.value.find(m => m.id === messageId)
      if (message) {
        message.rating = rating
      }
    } catch (error) {
      console.error('Failed to rate message:', error)
    }
  }
  
  const addUploadedFile = (file) => {
    uploadedFiles.value.push(file)
  }
  
  const removeUploadedFile = (fileId) => {
    uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== fileId)
  }
  
  const clearUploadedFiles = () => {
    uploadedFiles.value = []
  }
  
  return {
    // State
    conversations,
    currentConversation,
    messages,
    isTyping,
    isThinking,
    currentReasoningSteps,
    currentToolExecutions,
    uploadedFiles,
    
    // Computed
    hasMessages,
    lastMessage,
    conversationTitle,
    
    // Actions
    createNewConversation,
    loadConversations,
    loadConversation,
    sendMessage,
    regenerateLastMessage,
    deleteMessage,
    deleteConversation,
    rateMessage,
    addUploadedFile,
    removeUploadedFile,
    clearUploadedFiles
  }
})
