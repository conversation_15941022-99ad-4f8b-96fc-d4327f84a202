# WIDDX AI v2.0 - Quick Start Guide 🚀

This guide will help you get WIDDX AI v2.0 up and running in minutes!

## 📋 Prerequisites

Before you begin, ensure you have:

- **PHP 8.2+** with extensions: `mbstring`, `openssl`, `pdo`, `tokenizer`, `xml`, `curl`
- **Node.js 18+** and npm
- **MySQL 8.0+** or MariaDB 10.3+
- **Composer** (PHP dependency manager)
- **Git** for version control

### AI API Keys (Required)
You'll need at least one of these API keys:
- **DeepSeek API Key** - [Get it here](https://platform.deepseek.com)
- **Google Gemini API Key** - [Get it here](https://ai.google.dev)
- **HuggingFace API Key** - [Get it here](https://huggingface.co/settings/tokens)

## ⚡ Quick Installation

### 1. Clone and Setup
```bash
# Clone the repository
git clone https://github.com/your-username/widdx-ai.git
cd widdx-ai

# Install dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate
```

### 2. Database Configuration
Edit your `.env` file:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

Create the database:
```bash
# Create database (MySQL)
mysql -u root -p -e "CREATE DATABASE widdx_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
php artisan migrate
```

### 3. AI API Configuration
Add your AI API keys to `.env`:
```env
# At least one is required
DEEPSEEK_API_KEY=your_deepseek_api_key
GEMINI_API_KEY=your_gemini_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Optional: Set default model
AI_DEFAULT_MODEL=deepseek
```

### 4. Build and Start
```bash
# Build frontend assets
npm run build

# Start the application
php artisan serve
```

🎉 **That's it!** Open `http://localhost:8000` in your browser.

## 🔧 Development Mode

For development with hot reloading:

```bash
# Terminal 1: Start Laravel server
php artisan serve

# Terminal 2: Start Vite dev server
npm run dev
```

Access the app at `http://localhost:8000` with hot reloading enabled.

## 🌍 Multi-Language Setup

WIDDX AI v2.0 supports 5 languages out of the box:

- **English** (en) - Default
- **Arabic** (ar) - العربية
- **French** (fr) - Français  
- **Spanish** (es) - Español
- **German** (de) - Deutsch

No additional setup required! Switch languages using the language toggle in the interface.

## 🎨 Theme Configuration

Configure default theme in `.env`:
```env
# Theme options: light, dark, auto
APP_DEFAULT_THEME=dark

# Language options: en, ar, fr, es, de
APP_DEFAULT_LANGUAGE=en
```

## 🔐 Security Setup (Production)

For production deployment:

```bash
# Optimize for production
composer install --optimize-autoloader --no-dev
npm run build

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set secure environment
APP_ENV=production
APP_DEBUG=false
```

## 📱 Mobile & PWA

WIDDX AI v2.0 is fully responsive and PWA-ready:

- **Mobile Optimized**: Works perfectly on all screen sizes
- **Touch Friendly**: Optimized touch interactions
- **Offline Ready**: Basic offline functionality
- **App-like Experience**: Can be installed as a mobile app

## 🧪 Testing Your Installation

### 1. Basic Chat Test
1. Open the application
2. Type: "Hello, can you help me with coding?"
3. You should see a response with reasoning steps

### 2. Multi-Language Test
1. Click the language toggle (🌐)
2. Switch to Arabic or another language
3. Interface should update immediately

### 3. File Upload Test
1. Click the paperclip icon (📎)
2. Upload an image or text file
3. Ask AI to analyze it

### 4. Theme Test
1. Click the theme toggle (🌙/☀️)
2. Interface should switch between dark/light modes

## 🚨 Troubleshooting

### Common Issues

**1. "Class not found" errors**
```bash
composer dump-autoload
php artisan clear-compiled
```

**2. Frontend assets not loading**
```bash
npm run build
php artisan view:clear
```

**3. Database connection errors**
- Check MySQL is running
- Verify credentials in `.env`
- Ensure database exists

**4. API key errors**
- Verify API keys are correct
- Check API key permissions
- Ensure at least one AI service is configured

**5. Permission errors (Linux/Mac)**
```bash
sudo chown -R $USER:www-data storage
sudo chown -R $USER:www-data bootstrap/cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### Debug Mode

Enable debug mode for detailed error messages:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

### Check System Requirements
```bash
# Check PHP version and extensions
php -v
php -m | grep -E "(mbstring|openssl|pdo|tokenizer|xml|curl)"

# Check Node.js version
node -v
npm -v

# Check MySQL version
mysql --version
```

## 📊 Performance Optimization

### For Better Performance

1. **Enable Caching**
```env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

2. **Optimize Database**
```bash
php artisan db:seed --class=OptimizationSeeder
```

3. **Enable Compression**
```env
ASSET_COMPRESSION=true
```

## 🔄 Updates

To update WIDDX AI:

```bash
# Pull latest changes
git pull origin main

# Update dependencies
composer update
npm update

# Run migrations
php artisan migrate

# Rebuild assets
npm run build

# Clear caches
php artisan optimize:clear
```

## 📞 Getting Help

If you encounter issues:

1. **Check the logs**: `storage/logs/laravel.log`
2. **Browser console**: Check for JavaScript errors
3. **GitHub Issues**: [Report bugs](https://github.com/your-username/widdx-ai/issues)
4. **Discord**: [Join our community](https://discord.gg/widdx-ai)
5. **Email**: <EMAIL>

## 🎯 Next Steps

Once you have WIDDX AI running:

1. **Explore Features**: Try different AI models and languages
2. **Customize Settings**: Adjust AI personality and preferences
3. **Upload Files**: Test multi-modal capabilities
4. **Admin Panel**: Access `/admin` for system management
5. **API Integration**: Use the REST API for custom integrations

## 🏗️ Development

For developers wanting to contribute:

```bash
# Install development dependencies
npm install --include=dev

# Run tests
php artisan test
npm run test

# Code formatting
npm run lint
composer run-script format
```

---

**🎉 Congratulations!** You now have WIDDX AI v2.0 running with advanced multi-language support, chain-of-thought reasoning, and modern UI.

**Happy coding!** 🚀
