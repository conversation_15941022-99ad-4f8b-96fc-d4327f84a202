<template>
  <div class="messages-container" ref="messagesContainer">
    <!-- Welcome Message -->
    <div v-if="!chatStore.hasMessages" class="welcome-message">
      <div class="welcome-content">
        <div class="welcome-icon">
          <i class="fas fa-robot"></i>
        </div>
        <h2>{{ $t('app.tagline') }}</h2>
        <div class="welcome-text" v-html="$t('messages.welcome')"></div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
          <button 
            v-for="action in quickActions" 
            :key="action.id"
            class="quick-action-btn"
            @click="sendQuickMessage(action.message)"
          >
            <i :class="action.icon"></i>
            <span>{{ $t(action.label) }}</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Messages List -->
    <div v-for="message in chatStore.messages" :key="message.id" class="message" :class="message.role">
      <!-- Message Avatar -->
      <div class="message-avatar">
        <i v-if="message.role === 'user'" class="fas fa-user"></i>
        <i v-else-if="message.role === 'assistant'" class="fas fa-robot"></i>
        <i v-else class="fas fa-info-circle"></i>
      </div>
      
      <!-- Message Content -->
      <div class="message-content">
        <!-- Message Header -->
        <div class="message-header">
          <span class="message-sender">
            {{ getSenderName(message.role) }}
          </span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          <div v-if="message.confidence_score" class="confidence-score">
            <i class="fas fa-chart-line"></i>
            <span>{{ Math.round(message.confidence_score * 100) }}%</span>
          </div>
        </div>
        
        <!-- Message Text -->
        <div class="message-text" v-html="formatMessage(message.content)"></div>
        
        <!-- Reasoning Steps -->
        <div v-if="message.reasoning_steps && message.reasoning_steps.length > 0 && showReasoningSteps" class="reasoning-steps">
          <div class="reasoning-header">
            <h4>
              <i class="fas fa-brain"></i>
              {{ $t('reasoning.title') }}
            </h4>
            <button class="toggle-reasoning" @click="toggleReasoningDetails">
              <i :class="showReasoningDetails ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            </button>
          </div>
          
          <div v-if="showReasoningDetails" class="reasoning-content">
            <div 
              v-for="(step, index) in message.reasoning_steps" 
              :key="index" 
              class="reasoning-step"
            >
              <div class="step-header">
                <span class="step-number">{{ index + 1 }}</span>
                <span class="step-title">{{ step.type || $t('reasoning.step') }}</span>
                <span class="step-confidence">{{ Math.round((step.confidence || 0) * 100) }}%</span>
              </div>
              <div class="step-content">{{ step.content }}</div>
            </div>
          </div>
        </div>
        
        <!-- Tool Executions -->
        <div v-if="message.tool_executions && message.tool_executions.length > 0" class="tool-executions">
          <h4>
            <i class="fas fa-tools"></i>
            {{ $t('tools.title') }}
          </h4>
          
          <div 
            v-for="tool in message.tool_executions" 
            :key="tool.id" 
            class="tool-execution"
          >
            <div class="tool-header">
              <i :class="getToolIcon(tool.tool_name)"></i>
              <span class="tool-name">{{ $t(`tools.${tool.tool_name}`) || tool.tool_name }}</span>
              <span class="tool-status" :class="tool.status">
                {{ $t(`tools.${tool.status}`) || tool.status }}
              </span>
            </div>
            <div v-if="tool.result" class="tool-result">{{ tool.result }}</div>
          </div>
        </div>
        
        <!-- Message Actions -->
        <div class="message-actions">
          <button class="action-btn" @click="copyMessage(message.content)" :title="$t('chat.copy')">
            <i class="fas fa-copy"></i>
          </button>
          
          <button 
            v-if="message.role === 'assistant'" 
            class="action-btn" 
            @click="regenerateMessage"
            :title="$t('chat.regenerate')"
          >
            <i class="fas fa-redo"></i>
          </button>
          
          <button 
            class="action-btn" 
            @click="rateMessage(message.id, 'like')"
            :class="{ 'active': message.rating === 'like' }"
            :title="$t('chat.like')"
          >
            <i class="fas fa-thumbs-up"></i>
          </button>
          
          <button 
            class="action-btn" 
            @click="rateMessage(message.id, 'dislike')"
            :class="{ 'active': message.rating === 'dislike' }"
            :title="$t('chat.dislike')"
          >
            <i class="fas fa-thumbs-down"></i>
          </button>
          
          <button 
            class="action-btn" 
            @click="shareMessage(message)"
            :title="$t('chat.share')"
          >
            <i class="fas fa-share"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Typing Indicator -->
    <div v-if="chatStore.isTyping" class="typing-indicator">
      <div class="message-avatar">
        <i class="fas fa-robot"></i>
      </div>
      <div class="typing-content">
        <span>{{ $t('chat.thinking') }}</span>
        <div class="typing-dots">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useChatStore } from '../../stores/chat'
import { marked } from 'marked'

// Composables
const { t } = useI18n()
const chatStore = useChatStore()

// Refs
const messagesContainer = ref(null)

// Reactive state
const showReasoningSteps = ref(true)
const showReasoningDetails = ref(true)

// Computed
const quickActions = computed(() => [
  {
    id: 'help',
    icon: 'fas fa-question-circle',
    label: 'common.help',
    message: 'How can you help me today?'
  },
  {
    id: 'code',
    icon: 'fas fa-code',
    label: 'tools.codeAnalyzer',
    message: 'Can you help me with programming?'
  },
  {
    id: 'analyze',
    icon: 'fas fa-chart-bar',
    label: 'tools.dataAnalyzer',
    message: 'I need help analyzing some data'
  },
  {
    id: 'creative',
    icon: 'fas fa-lightbulb',
    label: 'personality.creativity',
    message: 'Help me brainstorm some creative ideas'
  }
])

// Methods
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const getSenderName = (role) => {
  switch (role) {
    case 'user': return t('messages.you')
    case 'assistant': return t('messages.assistant')
    case 'system': return t('messages.system')
    default: return role
  }
}

const formatMessage = (content) => {
  if (!content) return ''
  
  try {
    // Configure marked for better rendering
    marked.setOptions({
      breaks: true,
      gfm: true,
      sanitize: false
    })
    
    return marked(content)
  } catch (error) {
    console.error('Error formatting message:', error)
    return content.replace(/\n/g, '<br>')
  }
}

const getToolIcon = (toolName) => {
  const icons = {
    calculator: 'fas fa-calculator',
    webSearch: 'fas fa-search',
    codeAnalyzer: 'fas fa-code',
    dataAnalyzer: 'fas fa-chart-bar',
    summarizer: 'fas fa-compress-alt',
    translator: 'fas fa-language',
    imageAnalyzer: 'fas fa-image',
    fileReader: 'fas fa-file-alt'
  }
  return icons[toolName] || 'fas fa-tool'
}

const copyMessage = async (content) => {
  try {
    // Remove HTML tags for plain text copy
    const plainText = content.replace(/<[^>]*>/g, '')
    await navigator.clipboard.writeText(plainText)
    
    // Show success feedback
    console.log('Message copied to clipboard')
  } catch (error) {
    console.error('Failed to copy message:', error)
  }
}

const regenerateMessage = () => {
  chatStore.regenerateLastMessage()
}

const rateMessage = async (messageId, rating) => {
  try {
    await chatStore.rateMessage(messageId, rating)
  } catch (error) {
    console.error('Failed to rate message:', error)
  }
}

const shareMessage = async (message) => {
  try {
    const shareData = {
      title: 'WIDDX AI Conversation',
      text: message.content.replace(/<[^>]*>/g, ''),
      url: window.location.href
    }
    
    if (navigator.share) {
      await navigator.share(shareData)
    } else {
      // Fallback: copy to clipboard
      await copyMessage(message.content)
    }
  } catch (error) {
    console.error('Failed to share message:', error)
  }
}

const sendQuickMessage = (message) => {
  // Emit event to parent to send message
  window.dispatchEvent(new CustomEvent('send-quick-message', {
    detail: { message }
  }))
}

const toggleReasoningDetails = () => {
  showReasoningDetails.value = !showReasoningDetails.value
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    nextTick(() => {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    })
  }
}

// Event listeners
const handleToggleReasoningSteps = (event) => {
  showReasoningSteps.value = event.detail.show
}

// Lifecycle
onMounted(() => {
  scrollToBottom()
  window.addEventListener('toggle-reasoning-steps', handleToggleReasoningSteps)
})

onUnmounted(() => {
  window.removeEventListener('toggle-reasoning-steps', handleToggleReasoningSteps)
})

// Watch for new messages
watch(() => chatStore.messages.length, () => {
  scrollToBottom()
})

watch(() => chatStore.isTyping, () => {
  scrollToBottom()
})
</script>

<style scoped>
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Welcome Message */
.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 600px;
  padding: 2rem;
}

.welcome-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.welcome-content h2 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-text {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.quick-action-btn {
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-align: left;
  color: var(--text-primary);
}

.quick-action-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.quick-action-btn i {
  font-size: 1.25rem;
  color: var(--primary-500);
  transition: color 0.2s ease;
}

.quick-action-btn:hover i {
  color: white;
}

/* Message Styles */
.message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.4s ease-out;
  position: relative;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, var(--accent-500), var(--accent-600));
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.message.assistant .message-avatar {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.message.system .message-avatar {
  background: var(--warning-500);
  color: white;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.message-sender {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.confidence-score {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.5rem;
  background: var(--success-100);
  color: var(--success-700);
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: auto;
}

.message-text {
  background: var(--bg-secondary);
  padding: 1rem 1.25rem;
  border-radius: 1rem;
  line-height: 1.7;
  word-wrap: break-word;
  position: relative;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.message.user .message-text {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border-color: var(--primary-400);
  margin-left: auto;
  max-width: 85%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.message.assistant .message-text {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  position: relative;
  overflow: hidden;
}

.message.assistant .message-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  opacity: 0.6;
}

/* Reasoning Steps */
.reasoning-steps {
  margin-top: 1.25rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  border-left: 4px solid var(--primary-500);
}

.reasoning-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.reasoning-header h4 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary-600);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-reasoning {
  width: 32px;
  height: 32px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-reasoning:hover {
  background: var(--primary-500);
  color: white;
}

.reasoning-step {
  margin-bottom: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  animation: fadeInStep 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

.reasoning-step:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.reasoning-step:nth-child(1) { animation-delay: 0.1s; }
.reasoning-step:nth-child(2) { animation-delay: 0.2s; }
.reasoning-step:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInStep {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.step-number {
  width: 24px;
  height: 24px;
  background: var(--primary-500);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  flex-shrink: 0;
}

.step-title {
  font-weight: 600;
  color: var(--text-primary);
  text-transform: capitalize;
}

.step-confidence {
  margin-left: auto;
  padding: 0.125rem 0.5rem;
  background: var(--success-100);
  color: var(--success-700);
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.step-content {
  color: var(--text-secondary);
  line-height: 1.6;
  padding-left: 2rem;
}

/* Tool Executions */
.tool-executions {
  margin-top: 1.25rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05), rgba(34, 197, 94, 0.05));
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 1rem;
  border-left: 4px solid var(--accent-500);
}

.tool-executions h4 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--accent-600);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tool-execution {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  transition: all 0.2s ease;
}

.tool-execution:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tool-execution:last-child {
  margin-bottom: 0;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.tool-header i {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-500);
  font-size: 0.875rem;
}

.tool-name {
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.tool-status {
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tool-status.success {
  background: var(--success-100);
  color: var(--success-700);
}

.tool-status.failed {
  background: var(--error-100);
  color: var(--error-700);
}

.tool-status.running {
  background: var(--warning-100);
  color: var(--warning-700);
  animation: pulse 1.5s infinite;
}

.tool-result {
  background: var(--bg-secondary);
  padding: 0.75rem;
  border-radius: 0.5rem;
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Message Actions */
.message-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  flex-wrap: wrap;
}

.message:hover .message-actions {
  opacity: 1;
}

.action-btn {
  padding: 0.5rem 0.75rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.action-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-1px);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.action-btn.active {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.4s ease-out;
}

.typing-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-style: italic;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--primary-500);
  animation: typingDot 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .messages-container {
    padding: 1rem;
  }
  
  .message {
    gap: 0.75rem;
  }
  
  .message-avatar {
    width: 36px;
    height: 36px;
    font-size: 0.875rem;
  }
  
  .message.user .message-text {
    max-width: 90%;
  }
  
  .reasoning-steps,
  .tool-executions {
    padding: 1rem;
    margin-top: 1rem;
  }
  
  .step-content {
    padding-left: 1.5rem;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .messages-container {
    padding: 0.75rem;
  }
  
  .message {
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
  
  .message-text {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
  }
  
  .welcome-content {
    padding: 1rem;
  }
  
  .welcome-content h2 {
    font-size: 1.5rem;
  }
  
  .welcome-text {
    font-size: 1rem;
  }
}
</style>
