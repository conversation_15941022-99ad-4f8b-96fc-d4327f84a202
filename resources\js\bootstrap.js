/**
 * WIDDX AI v2.0 - Bootstrap
 * 
 * This file loads the necessary dependencies and configurations
 * for the WIDDX AI application.
 */

import axios from 'axios';
window.axios = axios;

// Set default headers
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// CSRF Token
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Global error handler for axios
window.axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 419) {
            // CSRF token mismatch
            window.location.reload();
        }
        return Promise.reject(error);
    }
);

// Global utilities
window.WiddxAI = window.WiddxAI || {};

// Console welcome message
console.log('%c🚀 WIDDX AI v2.0', 'color: #6366f1; font-size: 24px; font-weight: bold;');
console.log('%c🧠 Next Generation AI Assistant', 'color: #8b5cf6; font-size: 16px;');
console.log('%c🌍 Multi-language • 🎨 Modern UI • ⚡ Advanced Features', 'color: #06b6d4; font-size: 14px;');
