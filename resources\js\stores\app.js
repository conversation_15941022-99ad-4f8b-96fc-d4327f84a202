import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { setLanguage, getCurrentLanguage, isRTL } from '../i18n'

export const useAppStore = defineStore('app', () => {
  // State
  const theme = ref(localStorage.getItem('widdx-theme') || 'dark')
  const language = ref(getCurrentLanguage())
  const sidebarOpen = ref(true)
  const loading = ref(false)
  const error = ref(null)
  const online = ref(navigator.onLine)
  
  // Computed
  const isDark = computed(() => theme.value === 'dark')
  const isLight = computed(() => theme.value === 'light')
  const isRTLLanguage = computed(() => isRTL(language.value))
  
  // Actions
  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('widdx-theme', newTheme)
    
    // Update body class
    document.body.classList.remove('light-mode', 'dark-mode')
    document.body.classList.add(`${newTheme}-mode`)
  }
  
  const toggleTheme = () => {
    setTheme(isDark.value ? 'light' : 'dark')
  }
  
  const setLanguageAndDirection = (newLanguage) => {
    language.value = newLanguage
    setLanguage(newLanguage)
  }
  
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }
  
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }
  
  const setError = (errorMessage) => {
    error.value = errorMessage
  }
  
  const clearError = () => {
    error.value = null
  }
  
  const updateOnlineStatus = () => {
    online.value = navigator.onLine
  }
  
  // Initialize theme on load
  setTheme(theme.value)
  
  // Listen for online/offline events
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
  
  return {
    // State
    theme,
    language,
    sidebarOpen,
    loading,
    error,
    online,
    
    // Computed
    isDark,
    isLight,
    isRTLLanguage,
    
    // Actions
    setTheme,
    toggleTheme,
    setLanguageAndDirection,
    toggleSidebar,
    setLoading,
    setError,
    clearError,
    updateOnlineStatus
  }
})
