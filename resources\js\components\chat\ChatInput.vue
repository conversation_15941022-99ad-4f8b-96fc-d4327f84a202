<template>
  <div class="input-container">
    <div class="input-wrapper">
      <!-- File Uploads Preview -->
      <div v-if="uploadedFiles.length > 0" class="file-uploads">
        <div 
          v-for="file in uploadedFiles" 
          :key="file.id" 
          class="uploaded-file"
        >
          <i :class="getFileIcon(file.type)"></i>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">{{ formatFileSize(file.size) }}</span>
          <button class="remove-file" @click="removeFile(file.id)" :title="$t('files.remove')">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <!-- Input Row -->
      <div class="input-row" :class="{ 'focused': isFocused }">
        <!-- Message Input -->
        <textarea 
          ref="messageInput"
          v-model="messageText"
          class="message-input"
          :placeholder="$t('chat.placeholder')"
          @keydown="handleKeydown"
          @input="handleInput"
          @focus="isFocused = true"
          @blur="isFocused = false"
          @paste="handlePaste"
          rows="1"
          :disabled="chatStore.isTyping"
        ></textarea>
        
        <!-- Input Actions -->
        <div class="input-actions">
          <!-- File Upload -->
          <label class="input-action-btn file-upload-btn" :title="$t('files.upload')">
            <input 
              type="file" 
              multiple 
              @change="handleFileUpload" 
              accept="image/*,text/*,.pdf,.doc,.docx,.txt,.json,.csv"
              style="display: none;"
            >
            <i class="fas fa-paperclip"></i>
          </label>
          
          <!-- Voice Input -->
          <button 
            class="input-action-btn voice-btn" 
            @click="toggleVoiceInput"
            :class="{ 'active': isRecording }"
            :title="isRecording ? $t('common.stop') : $t('files.voice')"
          >
            <i :class="isRecording ? 'fas fa-stop' : 'fas fa-microphone'"></i>
          </button>
          
          <!-- Emoji Picker -->
          <button 
            class="input-action-btn emoji-btn" 
            @click="toggleEmojiPicker"
            :title="$t('common.emoji')"
          >
            <i class="fas fa-smile"></i>
          </button>
          
          <!-- Send Button -->
          <button 
            class="send-btn" 
            @click="sendMessage"
            :disabled="!canSend || chatStore.isTyping"
            :title="$t('chat.send')"
          >
            <i v-if="chatStore.isTyping" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
      
      <!-- Input Footer -->
      <div class="input-footer">
        <div class="input-hints">
          <span class="hint">{{ $t('chat.enterToSend') }}</span>
          <span class="hint">{{ $t('chat.shiftEnterNewLine') }}</span>
        </div>
        
        <div class="character-count" v-if="messageText.length > 0">
          {{ messageText.length }}/{{ maxLength }}
        </div>
      </div>
    </div>
    
    <!-- Emoji Picker -->
    <div v-if="showEmojiPicker" class="emoji-picker" @click.stop>
      <div class="emoji-header">
        <h4>{{ $t('common.emoji') }}</h4>
        <button class="close-emoji" @click="showEmojiPicker = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="emoji-grid">
        <button 
          v-for="emoji in commonEmojis" 
          :key="emoji"
          class="emoji-btn"
          @click="insertEmoji(emoji)"
        >
          {{ emoji }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useChatStore } from '../../stores/chat'

// Composables
const { t } = useI18n()
const chatStore = useChatStore()

// Refs
const messageInput = ref(null)

// Reactive state
const messageText = ref('')
const uploadedFiles = ref([])
const isFocused = ref(false)
const isRecording = ref(false)
const showEmojiPicker = ref(false)
const maxLength = ref(4000)

// Computed
const canSend = computed(() => {
  return (messageText.value.trim().length > 0 || uploadedFiles.value.length > 0) && 
         messageText.value.length <= maxLength.value
})

const commonEmojis = computed(() => [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
  '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
  '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
  '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
  '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
  '🔥', '💯', '💫', '⭐', '🌟', '✨', '⚡', '💥', '💢', '💨'
])

// Methods
const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  } else if (event.key === 'Escape') {
    showEmojiPicker.value = false
  }
}

const handleInput = () => {
  // Auto-resize textarea
  const textarea = messageInput.value
  if (textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'
  }
}

const handlePaste = async (event) => {
  const items = event.clipboardData?.items
  if (!items) return
  
  for (let item of items) {
    if (item.type.startsWith('image/')) {
      event.preventDefault()
      const file = item.getAsFile()
      if (file) {
        addFile(file)
      }
    }
  }
}

const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => addFile(file))
  
  // Clear input
  event.target.value = ''
}

const addFile = (file) => {
  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    alert(t('files.maxSizeExceeded'))
    return
  }
  
  const fileObj = {
    id: Date.now() + Math.random(),
    file,
    name: file.name,
    type: file.type,
    size: file.size
  }
  
  uploadedFiles.value.push(fileObj)
}

const removeFile = (fileId) => {
  uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== fileId)
}

const getFileIcon = (fileType) => {
  if (fileType.startsWith('image/')) return 'fas fa-image'
  if (fileType.startsWith('video/')) return 'fas fa-video'
  if (fileType.startsWith('audio/')) return 'fas fa-music'
  if (fileType.includes('pdf')) return 'fas fa-file-pdf'
  if (fileType.includes('word')) return 'fas fa-file-word'
  if (fileType.includes('excel') || fileType.includes('spreadsheet')) return 'fas fa-file-excel'
  if (fileType.includes('text') || fileType.includes('json')) return 'fas fa-file-alt'
  return 'fas fa-file'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const toggleVoiceInput = () => {
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    alert(t('voice.notSupported'))
    return
  }
  
  if (isRecording.value) {
    stopVoiceRecording()
  } else {
    startVoiceRecording()
  }
}

const startVoiceRecording = () => {
  try {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    const recognition = new SpeechRecognition()
    
    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = 'en-US' // TODO: Use current language
    
    recognition.onstart = () => {
      isRecording.value = true
    }
    
    recognition.onresult = (event) => {
      let transcript = ''
      for (let i = event.resultIndex; i < event.results.length; i++) {
        transcript += event.results[i][0].transcript
      }
      messageText.value = transcript
    }
    
    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error)
      isRecording.value = false
    }
    
    recognition.onend = () => {
      isRecording.value = false
    }
    
    recognition.start()
    window.currentRecognition = recognition
  } catch (error) {
    console.error('Voice recording error:', error)
    isRecording.value = false
  }
}

const stopVoiceRecording = () => {
  if (window.currentRecognition) {
    window.currentRecognition.stop()
    window.currentRecognition = null
  }
  isRecording.value = false
}

const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

const insertEmoji = (emoji) => {
  const textarea = messageInput.value
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  
  messageText.value = messageText.value.substring(0, start) + emoji + messageText.value.substring(end)
  
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + emoji.length, start + emoji.length)
  })
}

const sendMessage = async () => {
  if (!canSend.value) return
  
  const content = messageText.value.trim()
  const files = uploadedFiles.value.map(f => f.file)
  
  // Clear input
  messageText.value = ''
  uploadedFiles.value = []
  showEmojiPicker.value = false
  
  // Reset textarea height
  if (messageInput.value) {
    messageInput.value.style.height = 'auto'
  }
  
  try {
    await chatStore.sendMessage(content, files)
  } catch (error) {
    console.error('Failed to send message:', error)
    // Restore message on error
    messageText.value = content
    files.forEach(file => addFile(file))
  }
}

const focusInput = () => {
  if (messageInput.value) {
    messageInput.value.focus()
  }
}

// Event listeners
const handleSendQuickMessage = (event) => {
  messageText.value = event.detail.message
  sendMessage()
}

const handleClickOutside = (event) => {
  if (showEmojiPicker.value && !event.target.closest('.emoji-picker') && !event.target.closest('.emoji-btn')) {
    showEmojiPicker.value = false
  }
}

// Lifecycle
onMounted(() => {
  focusInput()
  window.addEventListener('send-quick-message', handleSendQuickMessage)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  window.removeEventListener('send-quick-message', handleSendQuickMessage)
  document.removeEventListener('click', handleClickOutside)
  
  // Stop voice recording if active
  if (isRecording.value) {
    stopVoiceRecording()
  }
})

// Expose methods for parent component
defineExpose({
  focusInput,
  clearInput: () => {
    messageText.value = ''
    uploadedFiles.value = []
  }
})
</script>

<style scoped>
.input-container {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  position: relative;
}

.input-wrapper {
  max-width: 1000px;
  margin: 0 auto;
}

/* File Uploads */
.file-uploads {
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.uploaded-file:hover {
  background: var(--bg-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.uploaded-file i {
  color: var(--primary-500);
  font-size: 1rem;
}

.file-name {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.remove-file {
  width: 20px;
  height: 20px;
  background: var(--error-500);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.remove-file:hover {
  background: var(--error-600);
  transform: scale(1.1);
}

/* Input Row */
.input-row {
  display: flex;
  align-items: end;
  gap: 0.75rem;
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
  position: relative;
}

.input-row.focused {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 200px;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
  font-family: inherit;
  resize: none;
  outline: none;
  line-height: 1.5;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.message-input::-webkit-scrollbar {
  width: 4px;
}

.message-input::-webkit-scrollbar-track {
  background: transparent;
}

.message-input::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Input Actions */
.input-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.input-action-btn,
.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.input-action-btn {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.input-action-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.voice-btn.active {
  background: var(--error-500);
  color: white;
  border-color: var(--error-500);
  animation: pulse 1.5s infinite;
}

.send-btn {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.send-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Input Footer */
.input-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.75rem;
  padding: 0 0.5rem;
}

.input-hints {
  display: flex;
  gap: 1rem;
}

.hint {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.character-count {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* Emoji Picker */
.emoji-picker {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 320px;
  max-height: 300px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  z-index: 100;
  margin-bottom: 0.5rem;
}

.emoji-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.emoji-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-emoji {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-emoji:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.25rem;
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-btn {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.emoji-btn:hover {
  background: var(--bg-tertiary);
  transform: scale(1.2);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .input-container {
    padding: 1rem;
  }
  
  .input-row {
    padding: 0.5rem;
  }
  
  .input-action-btn,
  .send-btn {
    width: 40px;
    height: 40px;
    font-size: 0.875rem;
  }
  
  .message-input {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
  
  .input-hints {
    gap: 0.5rem;
  }
  
  .hint {
    font-size: 0.625rem;
  }
  
  .emoji-picker {
    width: 280px;
  }
}

@media (max-width: 480px) {
  .input-container {
    padding: 0.75rem;
  }
  
  .input-row {
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .input-action-btn,
  .send-btn {
    width: 36px;
    height: 36px;
    font-size: 0.75rem;
  }
  
  .message-input {
    font-size: 0.875rem;
    padding: 0.5rem;
  }
  
  .uploaded-file {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .file-name {
    max-width: 100px;
  }
  
  .input-footer {
    margin-top: 0.5rem;
  }
  
  .input-hints {
    display: none; /* Hide hints on very small screens */
  }
  
  .emoji-picker {
    width: calc(100vw - 1.5rem);
    right: 0.75rem;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.05);
  }
}

/* Focus trap for accessibility */
.input-row:focus-within {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
</style>
