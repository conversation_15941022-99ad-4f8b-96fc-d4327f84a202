<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // 6. User Memory and Preferences
        Schema::create('user_memories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('memory_type'); // preference, fact, pattern, behavior
            $table->string('key'); // memory identifier
            $table->json('value'); // memory content
            $table->text('context')->nullable(); // when/how this was learned
            $table->float('confidence')->default(1.0);
            $table->integer('reinforcement_count')->default(1);
            $table->timestamp('last_reinforced_at');
            $table->timestamps();
            
            $table->unique(['user_id', 'memory_type', 'key']);
            $table->index(['user_id', 'memory_type']);
        });

        // 7. Model Performance and Routing
        Schema::create('model_performances', function (Blueprint $table) {
            $table->id();
            $table->string('model_name');
            $table->string('task_type'); // coding, research, creative, analysis
            $table->json('input_characteristics'); // length, complexity, language
            $table->float('success_rate');
            $table->float('avg_response_time');
            $table->float('user_satisfaction')->nullable();
            $table->integer('usage_count');
            $table->timestamp('last_updated_at');
            $table->timestamps();
            
            $table->unique(['model_name', 'task_type']);
        });

        // 8. File Uploads and Multi-Modal Data
        Schema::create('uploaded_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('conversation_id')->nullable()->constrained()->onDelete('set null');
            $table->string('original_name');
            $table->string('stored_name');
            $table->string('file_path');
            $table->string('mime_type');
            $table->bigInteger('file_size');
            $table->string('file_hash'); // for deduplication
            $table->enum('processing_status', ['pending', 'processing', 'completed', 'failed']);
            $table->json('extracted_data')->nullable(); // OCR, STT, document content
            $table->json('analysis_results')->nullable(); // AI analysis of content
            $table->timestamps();
            
            $table->index(['user_id', 'processing_status']);
            $table->index('file_hash');
        });

        // 9. Internal Tools and Chain of Thought
        Schema::create('ai_tools', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description');
            $table->string('handler_class'); // PHP class that implements the tool
            $table->json('parameters_schema'); // JSON schema for tool parameters
            $table->json('capabilities'); // what this tool can do
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->timestamps();
        });

        Schema::create('tool_executions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained()->onDelete('cascade');
            $table->foreignId('ai_tool_id')->constrained();
            $table->json('input_parameters');
            $table->json('output_result')->nullable();
            $table->enum('status', ['pending', 'running', 'completed', 'failed']);
            $table->text('error_message')->nullable();
            $table->float('execution_time')->nullable();
            $table->timestamps();
            
            $table->index(['message_id', 'status']);
        });

        // 10. System Configuration and Admin
        Schema::create('system_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->json('value');
            $table->text('description')->nullable();
            $table->string('category')->default('general');
            $table->boolean('is_user_configurable')->default(false);
            $table->timestamps();
        });

        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            $table->string('level'); // info, warning, error, debug
            $table->string('component'); // router, memory, learning, etc.
            $table->string('event');
            $table->json('data')->nullable();
            $table->foreignId('user_id')->nullable()->constrained();
            $table->timestamps();
            
            $table->index(['level', 'component', 'created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('system_configurations');
        Schema::dropIfExists('tool_executions');
        Schema::dropIfExists('ai_tools');
        Schema::dropIfExists('uploaded_files');
        Schema::dropIfExists('model_performances');
        Schema::dropIfExists('user_memories');
    }
};
