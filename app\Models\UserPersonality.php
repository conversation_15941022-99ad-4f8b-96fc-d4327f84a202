<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPersonality extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ai_personality_id',
        'customizations',
        'is_active'
    ];

    protected $casts = [
        'customizations' => 'array',
        'is_active' => 'boolean'
    ];

    /**
     * Get the user that owns this personality
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the AI personality
     */
    public function aiPersonality(): BelongsTo
    {
        return $this->belongsTo(AiPersonality::class);
    }

    /**
     * Scope for active user personalities
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
