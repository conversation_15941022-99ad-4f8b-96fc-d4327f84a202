/* WIDDX AI v2.0 - Advanced Chat Interface Styles */

:root {
    /* Color Palette */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Dark Theme */
    --dark-bg: #0f172a;
    --dark-surface: #1e293b;
    --dark-surface-hover: #334155;
    --dark-border: #334155;
    --dark-text: #f1f5f9;
    --dark-text-secondary: #94a3b8;
    --dark-text-muted: #64748b;
    
    /* Light Theme */
    --light-bg: #ffffff;
    --light-surface: #f8fafc;
    --light-surface-hover: #f1f5f9;
    --light-border: #e2e8f0;
    --light-text: #1e293b;
    --light-text-secondary: #64748b;
    --light-text-muted: #94a3b8;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
}

/* Enhanced Message Styles */
.message-text {
    position: relative;
    overflow: hidden;
}

.message-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.ai-message .message-text::before {
    opacity: 0.3;
}

/* Reasoning Steps Enhancement */
.reasoning-steps {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(99, 102, 241, 0.05);
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.reasoning-steps::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
}

.reasoning-step {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
    animation: fadeInStep 0.3s ease forwards;
    opacity: 0;
    transform: translateY(10px);
}

.reasoning-step:last-child {
    border-bottom: none;
}

.reasoning-step:nth-child(1) { animation-delay: 0.1s; }
.reasoning-step:nth-child(2) { animation-delay: 0.2s; }
.reasoning-step:nth-child(3) { animation-delay: 0.3s; }
.reasoning-step:nth-child(4) { animation-delay: 0.4s; }
.reasoning-step:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInStep {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tool Executions Enhancement */
.tool-executions {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--success-color);
}

.tool-execution {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(16, 185, 129, 0.05);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.tool-execution:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateX(2px);
}

.tool-execution.failed {
    background: rgba(239, 68, 68, 0.05);
    border-left: 2px solid var(--error-color);
}

/* Confidence Score Enhancement */
.confidence-score {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.confidence-high { color: var(--success-color); }
.confidence-medium { color: var(--warning-color); }
.confidence-low { color: var(--error-color); }

/* Enhanced Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 0.3s ease;
}

.typing-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--dark-text-secondary);
    font-style: italic;
    font-size: 0.875rem;
}

.light-mode .typing-status {
    color: var(--light-text-secondary);
}

.typing-dots {
    display: flex;
    gap: var(--spacing-xs);
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: typingDot 1.4s infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* Enhanced Message Actions */
.message-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.message:hover .message-actions {
    opacity: 1;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: transparent;
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.light-mode .action-btn {
    border: 1px solid var(--light-border);
    color: var(--light-text-secondary);
}

.action-btn:hover {
    background: var(--dark-surface-hover);
    color: var(--dark-text);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.light-mode .action-btn:hover {
    background: var(--light-surface-hover);
    color: var(--light-text);
}

.action-btn:active {
    transform: translateY(0);
}

/* Enhanced Input Area */
.input-container {
    position: relative;
    background: var(--dark-surface);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--dark-border);
}

.light-mode .input-container {
    background: var(--light-surface);
    border-top: 1px solid var(--light-border);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: end;
    gap: var(--spacing-md);
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing-xl);
}

.message-input {
    flex: 1;
    min-height: 44px;
    max-height: 200px;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    color: var(--dark-text);
    font-size: 0.875rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    transition: all var(--transition-normal);
    font-family: inherit;
}

.light-mode .message-input {
    background: var(--light-bg);
    border: 1px solid var(--light-border);
    color: var(--light-text);
}

.message-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: var(--dark-surface);
}

.light-mode .message-input:focus {
    background: var(--light-surface);
}

.message-input::placeholder {
    color: var(--dark-text-muted);
}

.light-mode .message-input::placeholder {
    color: var(--light-text-muted);
}

/* Enhanced Input Buttons */
.input-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.input-btn {
    width: 44px;
    height: 44px;
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.input-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.input-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.input-btn:hover::before {
    left: 100%;
}

.input-btn:active {
    transform: translateY(0);
}

.input-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-btn:disabled:hover {
    background: var(--primary-color);
    transform: none;
    box-shadow: none;
}

/* File Upload Styles */
.file-upload-area {
    position: relative;
    margin-top: var(--spacing-md);
    padding: var(--spacing-lg);
    border: 2px dashed var(--dark-border);
    border-radius: var(--radius-md);
    text-align: center;
    transition: all var(--transition-normal);
    background: rgba(99, 102, 241, 0.02);
}

.light-mode .file-upload-area {
    border: 2px dashed var(--light-border);
    background: rgba(99, 102, 241, 0.02);
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.file-preview {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.file-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.light-mode .file-item {
    background: var(--light-surface);
    border: 1px solid var(--light-border);
}

.file-remove {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0;
    font-size: 0.875rem;
}

/* Enhanced Scrollbar */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--dark-border);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--dark-text-secondary);
}

.light-mode .messages-container::-webkit-scrollbar-thumb {
    background: var(--light-border);
}

.light-mode .messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--light-text-secondary);
}

/* Enhanced Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
    }
}

.message {
    animation: messageSlideIn 0.4s ease;
}

.ai-message .message-avatar {
    animation: pulseGlow 3s infinite;
}

/* Responsive Enhancements */
@media (max-width: 640px) {
    .input-wrapper {
        padding: var(--spacing-md);
    }
    
    .message {
        margin-bottom: var(--spacing-lg);
    }
    
    .message-text {
        font-size: 0.875rem;
    }
    
    .reasoning-steps,
    .tool-executions {
        padding: var(--spacing-md);
        margin-top: var(--spacing-md);
    }
}
