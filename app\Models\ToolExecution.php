<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ToolExecution extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'ai_tool_id',
        'input_parameters',
        'output_result',
        'status',
        'error_message',
        'execution_time'
    ];

    protected $casts = [
        'input_parameters' => 'array',
        'output_result' => 'array',
        'execution_time' => 'float'
    ];

    /**
     * Get the message this execution belongs to
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Get the AI tool that was executed
     */
    public function aiTool(): BelongsTo
    {
        return $this->belongsTo(AiTool::class);
    }

    /**
     * Mark execution as completed
     */
    public function markCompleted(array $result, float $executionTime): void
    {
        $this->update([
            'status' => 'completed',
            'output_result' => $result,
            'execution_time' => $executionTime
        ]);
    }

    /**
     * Mark execution as failed
     */
    public function markFailed(string $errorMessage, float $executionTime): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'execution_time' => $executionTime
        ]);
    }

    /**
     * Scope for successful executions
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed executions
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for specific tool
     */
    public function scopeForTool($query, int $toolId)
    {
        return $query->where('ai_tool_id', $toolId);
    }
}
