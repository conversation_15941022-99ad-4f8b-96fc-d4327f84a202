<?php

namespace App\Services\Core;

use App\Models\AiPersonality;
use App\Models\UserPersonality;
use App\Models\UserMemory;
use Illuminate\Support\Facades\Cache;

/**
 * Digital Personality & User Memory Engine
 * Manages AI personality traits, user preferences, and long-term memory
 */
class PersonalityEngine
{
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Get active personality for user with customizations
     */
    public function getPersonalityForUser(int $userId): array
    {
        $cacheKey = "personality.user.{$userId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            $userPersonality = UserPersonality::with('aiPersonality')
                ->where('user_id', $userId)
                ->where('is_active', true)
                ->first();

            if (!$userPersonality) {
                // Create default personality for new user
                $defaultPersonality = AiPersonality::where('name', 'WIDDX-Default')->first();
                $userPersonality = UserPersonality::create([
                    'user_id' => $userId,
                    'ai_personality_id' => $defaultPersonality->id,
                    'customizations' => []
                ]);
                $userPersonality->load('aiPersonality');
            }

            $personality = $userPersonality->aiPersonality->toArray();

            // Apply user customizations
            if ($userPersonality->customizations) {
                $personality = array_merge_recursive($personality, $userPersonality->customizations);
            }

            return $personality;
        });
    }

    /**
     * Update user personality customizations
     */
    public function updateUserPersonality(int $userId, array $customizations): void
    {
        UserPersonality::where('user_id', $userId)
            ->where('is_active', true)
            ->update(['customizations' => $customizations]);

        Cache::forget("personality.user.{$userId}");
    }

    /**
     * Store user memory (preferences, facts, patterns)
     */
    public function storeMemory(int $userId, string $type, string $key, $value, string $context = null): void
    {
        UserMemory::updateOrCreate(
            [
                'user_id' => $userId,
                'memory_type' => $type,
                'key' => $key
            ],
            [
                'value' => is_array($value) ? $value : ['data' => $value],
                'context' => $context,
                'last_reinforced_at' => now(),
                'reinforcement_count' => \Illuminate\Support\Facades\DB::raw('reinforcement_count + 1')
            ]
        );

        Cache::forget("memories.user.{$userId}");
    }

    /**
     * Retrieve user memories by type
     */
    public function getMemories(int $userId, string $type = null): array
    {
        $cacheKey = "memories.user.{$userId}" . ($type ? ".{$type}" : '');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId, $type) {
            $query = UserMemory::where('user_id', $userId);

            if ($type) {
                $query->where('memory_type', $type);
            }

            return $query->orderBy('last_reinforced_at', 'desc')
                ->get()
                ->groupBy('memory_type')
                ->toArray();
        });
    }

    /**
     * Analyze conversation for memory extraction
     */
    public function extractMemoriesFromConversation(int $userId, array $messages): array
    {
        $extractedMemories = [];

        foreach ($messages as $message) {
            if ($message['role'] === 'user') {
                // Extract preferences
                $preferences = $this->extractPreferences($message['content']);
                foreach ($preferences as $key => $value) {
                    $extractedMemories[] = [
                        'type' => 'preference',
                        'key' => $key,
                        'value' => $value,
                        'context' => "Extracted from: " . substr($message['content'], 0, 100)
                    ];
                }

                // Extract facts about user
                $facts = $this->extractUserFacts($message['content']);
                foreach ($facts as $key => $value) {
                    $extractedMemories[] = [
                        'type' => 'fact',
                        'key' => $key,
                        'value' => $value,
                        'context' => "Mentioned in conversation"
                    ];
                }
            }
        }

        // Store extracted memories
        foreach ($extractedMemories as $memory) {
            $this->storeMemory(
                $userId,
                $memory['type'],
                $memory['key'],
                $memory['value'],
                $memory['context']
            );
        }

        return $extractedMemories;
    }

    /**
     * Extract user preferences from text
     */
    private function extractPreferences(string $text): array
    {
        $preferences = [];

        // Language preference
        if (preg_match('/prefer.*(arabic|english|عربي|إنجليزي)/i', $text, $matches)) {
            $preferences['language'] = strtolower($matches[1]);
        }

        // Response style
        if (preg_match('/want.*(detailed|brief|short|long|مفصل|مختصر)/i', $text, $matches)) {
            $preferences['response_style'] = strtolower($matches[1]);
        }

        // Technical level
        if (preg_match('/(beginner|intermediate|advanced|مبتدئ|متوسط|متقدم)/i', $text, $matches)) {
            $preferences['technical_level'] = strtolower($matches[1]);
        }

        return $preferences;
    }

    /**
     * Extract facts about user from text
     */
    private function extractUserFacts(string $text): array
    {
        $facts = [];

        // Programming languages
        $programmingLanguages = ['php', 'javascript', 'python', 'java', 'c++', 'c#', 'go', 'rust'];
        foreach ($programmingLanguages as $lang) {
            if (stripos($text, $lang) !== false) {
                $facts['programming_languages'][] = $lang;
            }
        }

        // Profession/Role
        $roles = ['developer', 'designer', 'manager', 'student', 'teacher', 'مطور', 'مصمم', 'مدير', 'طالب', 'معلم'];
        foreach ($roles as $role) {
            if (stripos($text, $role) !== false) {
                $facts['role'] = $role;
                break;
            }
        }

        return $facts;
    }

    /**
     * Apply personality to response
     */
    public function applyPersonalityToResponse(array $personality, string $response): string
    {
        $traits = $personality['traits'] ?? [];

        // Apply tone modifications
        if (isset($traits['tone'])) {
            $response = $this->applyTone($response, $traits['tone']);
        }

        // Apply verbosity level
        if (isset($traits['verbosity'])) {
            $response = $this->applyVerbosity($response, $traits['verbosity']);
        }

        // Apply emoji usage
        if (isset($traits['emoji_usage']) && $traits['emoji_usage'] === 'high') {
            $response = $this->addEmojis($response);
        }

        return $response;
    }

    private function applyTone(string $response, string $tone): string
    {
        // This would be enhanced with more sophisticated NLP
        switch ($tone) {
            case 'friendly':
                return "😊 " . $response;
            case 'professional':
                return str_replace(['!', '😊'], ['', ''], $response);
            case 'enthusiastic':
                return $response . " 🚀";
            default:
                return $response;
        }
    }

    private function applyVerbosity(string $response, string $level): string
    {
        switch ($level) {
            case 'brief':
                // Summarize response (simplified)
                $sentences = explode('.', $response);
                return implode('.', array_slice($sentences, 0, 2)) . '.';
            case 'detailed':
                return $response . "\n\nWould you like me to elaborate on any specific aspect?";
            default:
                return $response;
        }
    }

    private function addEmojis(string $response): string
    {
        $emojiMap = [
            'code' => '💻',
            'data' => '📊',
            'idea' => '💡',
            'success' => '✅',
            'error' => '❌',
            'thinking' => '🤔'
        ];

        foreach ($emojiMap as $keyword => $emoji) {
            if (stripos($response, $keyword) !== false) {
                $response = str_ireplace($keyword, $emoji . ' ' . $keyword, $response);
                break; // Add only one emoji per response
            }
        }

        return $response;
    }
}
