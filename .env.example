APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# AI Services Configuration
DEFAULT_AI_SERVICE=deepseek

# DeepSeek Configuration
DEEPSEEK_ENABLED=true
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=1000
DEEPSEEK_TIMEOUT=30

# Gemini Configuration
GEMINI_ENABLED=true
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=1000
GEMINI_TIMEOUT=30

# HuggingFace Configuration
HUGGINGFACE_ENABLED=true
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models
HUGGINGFACE_MODEL=mistralai/Mistral-7B-Instruct-v0.2
HUGGINGFACE_TEMPERATURE=0.7
HUGGINGFACE_MAX_TOKENS=1000
HUGGINGFACE_TIMEOUT=30

# AI Fallback Configuration
AI_FALLBACK_ENABLED=true

# AI Rate Limiting
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_MAX=60
AI_RATE_LIMIT_PERIOD=60

# Knowledge Base Configuration
KNOWLEDGE_BASE_ENABLED=true
KNOWLEDGE_BASE_CACHE_ENABLED=true
KNOWLEDGE_BASE_CACHE_TTL=3600
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD=0.7
KNOWLEDGE_BASE_EMBEDDING_DIMENSION=384
KNOWLEDGE_BASE_USE_MOCK_EMBEDDINGS=true
KNOWLEDGE_BASE_EMBEDDING_SERVICE_URL=https://api-inference.huggingface.co/pipeline/feature-extraction/sentence-transformers/all-MiniLM-L6-v2

# AI Caching Configuration
AI_CACHING_ENABLED=true
AI_CACHING_AI_RESPONSE_TTL=7200
AI_CACHING_CONVERSATIONS_LIST_TTL=900
AI_CACHING_CONVERSATION_TTL=3600
AI_CACHING_KNOWLEDGE_STATS_TTL=1800

# WIDDX AI v2.0 Advanced Features
WIDDX_SELF_LEARNING=true
WIDDX_CHAIN_OF_THOUGHT=true
WIDDX_MULTI_MODAL=true
WIDDX_PERSONALITY_ENGINE=true
WIDDX_MULTI_AGENT_ROUTING=true
WIDDX_INTERNAL_TOOLS=true
WIDDX_VOICE_INPUT=false
WIDDX_REAL_TIME_TYPING=true

# Personality Engine Settings
WIDDX_MEMORY_RETENTION_DAYS=365
WIDDX_CONFIDENCE_THRESHOLD=0.7
WIDDX_LEARNING_RATE=0.1
WIDDX_MAX_MEMORIES_PER_USER=10000

# Self-Learning Configuration
WIDDX_LEARNING_ENABLED=true
WIDDX_MIN_INTERACTION_QUALITY=3
WIDDX_AUTO_CLEANUP_DAYS=30
WIDDX_MAX_KNOWLEDGE_ENTRIES=100000
WIDDX_EMBEDDING_MODEL=text-embedding-ada-002
WIDDX_SIMILARITY_THRESHOLD=0.8

# Chain of Thought Settings
WIDDX_REASONING_ENABLED=true
WIDDX_MAX_REASONING_STEPS=10
WIDDX_REASONING_CONFIDENCE_THRESHOLD=0.6
WIDDX_SHOW_REASONING_STEPS=true

# Multi-Modal Support
WIDDX_MAX_FILE_SIZE=10240
WIDDX_STORAGE_DISK=local
WIDDX_OCR_ENABLED=false
WIDDX_STT_ENABLED=false

# Internal Tools
WIDDX_WEB_SEARCH_ENABLED=false
SEARCH_API_KEY=your_search_api_key_here

# Performance Settings
WIDDX_CACHE_TTL=3600
WIDDX_MAX_CONCURRENT_REQUESTS=10
WIDDX_RESPONSE_TIMEOUT=120
WIDDX_RATE_LIMIT_PER_MINUTE=60

# UI Configuration
WIDDX_DEFAULT_THEME=dark
WIDDX_DEFAULT_LANGUAGE=ar
WIDDX_MESSAGES_PER_PAGE=50
WIDDX_CONVERSATIONS_PER_PAGE=20
WIDDX_AUTO_SCROLL=true
WIDDX_TYPING_INDICATOR=true
WIDDX_SOUND_NOTIFICATIONS=false

# Security Settings
WIDDX_ENCRYPT_CONVERSATIONS=true
WIDDX_LOG_USER_ACTIONS=true
WIDDX_SESSION_TIMEOUT=7200
WIDDX_MAX_LOGIN_ATTEMPTS=5
WIDDX_REQUIRE_EMAIL_VERIFICATION=false

# Monitoring & Analytics
WIDDX_MONITORING_ENABLED=true
WIDDX_TRACK_PERFORMANCE=true
WIDDX_TRACK_USER_SATISFACTION=true
WIDDX_ERROR_REPORTING=true
WIDDX_METRICS_RETENTION_DAYS=90

# Admin Panel Settings
WIDDX_ADMIN_ENABLED=true
WIDDX_ADMIN_REQUIRE_2FA=false
WIDDX_ADMIN_SESSION_TIMEOUT=3600
WIDDX_ADMIN_ALLOWED_IPS=
WIDDX_BACKUP_ENABLED=true
WIDDX_BACKUP_FREQUENCY=daily

# API Configuration
WIDDX_API_RATE_LIMIT=100
WIDDX_API_PAGINATION_LIMIT=100
WIDDX_API_ENABLE_CORS=true
WIDDX_API_ALLOWED_ORIGINS=*
WIDDX_API_REQUIRE_AUTH=true

# Development Settings
WIDDX_DEBUG=false
WIDDX_LOG_LEVEL=info
WIDDX_SHOW_REASONING_DETAILS=false
WIDDX_MOCK_RESPONSES=false
WIDDX_PERFORMANCE_PROFILING=false
