<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| WIDDX AI v2.0 Web Routes
|--------------------------------------------------------------------------
*/

// Redirect root to chat
Route::get('/', fn() => redirect('/chat'));

// Main chat interface
Route::get('/chat', fn() => view('chat.index'))->name('chat.index');

// Admin panel routes
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', fn() => view('admin.dashboard'))->name('admin.dashboard');
    Route::get('/conversations', fn() => view('admin.conversations'))->name('admin.conversations');
    Route::get('/users', fn() => view('admin.users'))->name('admin.users');
    Route::get('/knowledge', fn() => view('admin.knowledge'))->name('admin.knowledge');
    Route::get('/models', fn() => view('admin.models'))->name('admin.models');
    Route::get('/settings', fn() => view('admin.settings'))->name('admin.settings');
});

// API routes are in api_v2.php
