<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\Message;
use App\Services\Core\PersonalityEngine;
use App\Services\Core\MultiAgentRouter;
use App\Services\Core\ChainOfThoughtEngine;
use App\Services\Core\SelfLearningEngine;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Next-Generation Conversation Controller
 * Handles advanced AI conversations with personality, reasoning, and learning
 */
class ConversationController extends Controller
{
    private PersonalityEngine $personalityEngine;
    private MultiAgentRouter $router;
    private ChainOfThoughtEngine $reasoningEngine;
    private SelfLearningEngine $learningEngine;

    public function __construct(
        PersonalityEngine $personalityEngine,
        MultiAgentRouter $router,
        ChainOfThoughtEngine $reasoningEngine,
        SelfLearningEngine $learningEngine
    ) {
        $this->personalityEngine = $personalityEngine;
        $this->router = $router;
        $this->reasoningEngine = $reasoningEngine;
        $this->learningEngine = $learningEngine;
    }

    /**
     * Get user's conversations
     */
    public function index(Request $request): JsonResponse
    {
        $conversations = Conversation::where('user_id', Auth::id())
            ->where('status', 'active')
            ->with(['messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->orderBy('last_activity_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $conversations
        ]);
    }

    /**
     * Create new conversation
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'personality_id' => 'nullable|exists:ai_personalities,id',
            'initial_message' => 'nullable|string|max:10000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Get user's active personality
            $personality = $this->personalityEngine->getPersonalityForUser(Auth::id());
            
            $conversation = Conversation::create([
                'user_id' => Auth::id(),
                'ai_personality_id' => $request->personality_id ?? $personality['id'],
                'title' => $request->title ?? 'New Conversation',
                'context' => [],
                'memory_tags' => [],
                'last_activity_at' => now()
            ]);

            // Send initial message if provided
            if ($request->initial_message) {
                $messageResponse = $this->processMessage(
                    $conversation,
                    $request->initial_message,
                    []
                );
                
                $conversation->load('messages');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $conversation,
                'message_response' => $messageResponse ?? null
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create conversation', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create conversation'
            ], 500);
        }
    }

    /**
     * Send message to conversation
     */
    public function sendMessage(Request $request, Conversation $conversation): JsonResponse
    {
        $this->authorize('update', $conversation);

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:10000',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max
            'use_reasoning' => 'boolean',
            'preferred_model' => 'nullable|in:deepseek,gemini,huggingface'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Process attachments if any
            $attachmentData = [];
            if ($request->hasFile('attachments')) {
                $attachmentData = $this->processAttachments($request->file('attachments'));
            }

            // Get conversation context
            $context = $this->buildConversationContext($conversation);

            // Process the message
            $response = $this->processMessage(
                $conversation,
                $request->message,
                $context,
                $attachmentData,
                $request->boolean('use_reasoning', true),
                $request->preferred_model
            );

            // Update conversation activity
            $conversation->update([
                'last_activity_at' => now(),
                'context' => array_merge($conversation->context ?? [], $response['context_updates'] ?? [])
            ]);

            // Learn from this interaction (async)
            if (config('widdx.self_learning_enabled', true)) {
                dispatch(function () use ($conversation) {
                    $this->learningEngine->learnFromConversation($conversation->id, $conversation->user_id);
                })->afterResponse();
            }

            return response()->json([
                'success' => true,
                'data' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process message', [
                'conversation_id' => $conversation->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process message'
            ], 500);
        }
    }

    /**
     * Get conversation messages
     */
    public function getMessages(Request $request, Conversation $conversation): JsonResponse
    {
        $this->authorize('view', $conversation);

        $messages = $conversation->messages()
            ->with(['toolExecutions'])
            ->orderBy('created_at', 'asc')
            ->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => $messages
        ]);
    }

    /**
     * Get reasoning steps for a conversation
     */
    public function getReasoningSteps(Request $request, Conversation $conversation): JsonResponse
    {
        $this->authorize('view', $conversation);

        $messages = $conversation->messages()
            ->whereNotNull('reasoning_steps')
            ->orderBy('created_at', 'desc')
            ->limit($request->get('limit', 10))
            ->get();

        $reasoningData = $messages->map(function ($message) {
            return [
                'message_id' => $message->id,
                'content' => $message->content,
                'reasoning_steps' => $message->reasoning_steps,
                'tool_calls' => $message->tool_calls,
                'confidence_score' => $message->confidence_score,
                'created_at' => $message->created_at
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $reasoningData
        ]);
    }

    /**
     * Regenerate AI response
     */
    public function regenerateResponse(Request $request, Conversation $conversation): JsonResponse
    {
        $this->authorize('update', $conversation);

        $validator = Validator::make($request->all(), [
            'message_id' => 'required|exists:messages,id',
            'use_different_model' => 'boolean',
            'preferred_model' => 'nullable|in:deepseek,gemini,huggingface'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $originalMessage = Message::findOrFail($request->message_id);
            
            // Get the user message that prompted this response
            $userMessage = Message::where('conversation_id', $conversation->id)
                ->where('created_at', '<', $originalMessage->created_at)
                ->where('role', 'user')
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$userMessage) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot find original user message'
                ], 400);
            }

            // Build context up to that point
            $context = $this->buildConversationContext($conversation, $originalMessage->created_at);

            // Generate new response
            $response = $this->processMessage(
                $conversation,
                $userMessage->content,
                $context,
                [],
                true,
                $request->preferred_model,
                $request->boolean('use_different_model', false)
            );

            // Mark original message as regenerated
            $originalMessage->update([
                'metadata' => array_merge($originalMessage->metadata ?? [], [
                    'regenerated_at' => now(),
                    'regenerated_by' => Auth::id()
                ])
            ]);

            return response()->json([
                'success' => true,
                'data' => $response,
                'original_message_id' => $originalMessage->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to regenerate response', [
                'conversation_id' => $conversation->id,
                'message_id' => $request->message_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate response'
            ], 500);
        }
    }

    /**
     * Process a message through the AI pipeline
     */
    private function processMessage(
        Conversation $conversation,
        string $messageContent,
        array $context = [],
        array $attachments = [],
        bool $useReasoning = true,
        ?string $preferredModel = null,
        bool $forceDifferentModel = false
    ): array {
        // Store user message
        $userMessage = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => $messageContent,
            'attachments' => $attachments,
            'metadata' => [
                'timestamp' => now(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]
        ]);

        // Get user personality
        $personality = $this->personalityEngine->getPersonalityForUser($conversation->user_id);

        // Process with reasoning if enabled
        if ($useReasoning) {
            $reasoningResult = $this->reasoningEngine->processWithReasoning($messageContent, $context);
            $aiResponse = $reasoningResult['response'];
            $reasoningSteps = $reasoningResult['reasoning_chain'];
            $toolExecutions = $reasoningResult['tool_executions'];
            $confidence = $reasoningResult['metadata']['overall_confidence'];
        } else {
            // Direct routing without reasoning
            $routingResult = $this->router->route($messageContent, $context);
            $aiResponse = $routingResult['response'];
            $reasoningSteps = null;
            $toolExecutions = [];
            $confidence = 0.8; // Default confidence
        }

        // Apply personality to response
        $personalizedResponse = $this->personalityEngine->applyPersonalityToResponse($personality, $aiResponse);

        // Store AI message
        $aiMessage = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => $personalizedResponse,
            'metadata' => [
                'model_used' => $preferredModel ?? 'widdx-ai-unified',
                'confidence_score' => $confidence,
                'personality_applied' => true,
                'reasoning_enabled' => $useReasoning,
                'processing_time' => microtime(true) - LARAVEL_START
            ],
            'reasoning_steps' => $reasoningSteps,
            'tool_calls' => $toolExecutions,
            'confidence_score' => $confidence,
            'processed_at' => now()
        ]);

        // Extract and store memories
        $this->personalityEngine->extractMemoriesFromConversation(
            $conversation->user_id,
            [$userMessage->toArray(), $aiMessage->toArray()]
        );

        return [
            'user_message' => $userMessage,
            'ai_message' => $aiMessage,
            'reasoning_steps' => $reasoningSteps,
            'tool_executions' => $toolExecutions,
            'confidence_score' => $confidence,
            'context_updates' => [
                'last_topic' => $this->extractTopic($messageContent),
                'interaction_count' => ($context['interaction_count'] ?? 0) + 1
            ]
        ];
    }

    // Helper methods continue in next part...
}
