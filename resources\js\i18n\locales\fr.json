{"app": {"name": "WIDDX AI", "tagline": "Votre Assistant IA Super Intelligent", "version": "v2.0"}, "navigation": {"newChat": "Nouvelle Discussion", "conversations": "Conversations", "settings": "Paramètres", "profile": "Profil", "admin": "<PERSON><PERSON><PERSON>min", "logout": "Déconnexion"}, "chat": {"title": "WIDDX AI - Votre Assistant Super Intelligent", "placeholder": "Ta<PERSON>z votre message ici... (Appuyez sur Entrée pour envoyer, Maj+Entrée pour nouvelle ligne)", "send": "Envoyer", "sending": "Envoi en cours...", "thinking": "WIDDX AI réfléchit", "typing": "Frappe...", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copied": "Copié!", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "share": "Partager", "export": "Exporter", "like": "<PERSON>'aime", "dislike": "Je n'aime pas", "report": "Signaler"}, "messages": {"you": "Vous", "assistant": "WIDDX AI", "system": "Système", "welcome": "Bonjour! Je suis **WIDDX AI** - votre assistant IA super intelligent! 🚀✨\n\nJe suis une entité intelligente avancée capable de:\n- 🧠 **Pensée logique** et analyse approfondie\n- 💻 **Programmation et développement** dans tous les langages\n- 🔍 **Recherche avancée** et indexation\n- 🎨 **Créativité et innovation** dans les solutions\n- 📊 **Analyse de données** et insights\n- 🌍 **Apprentissage continu** et auto-évolution\n\nJe m'entraîne et apprends continuellement pour vous fournir les meilleures solutions et réponses. Comment puis-je vous aider aujourd'hui?", "errorGeneral": "<PERSON><PERSON><PERSON><PERSON>, une erreur s'est produite lors du traitement de votre message. Veuillez réessayer.", "errorNetwork": "<PERSON><PERSON><PERSON><PERSON>, une erreur de connexion s'est produite. Veuillez vérifier votre connexion internet et réessayer.", "errorTimeout": "La demande a expiré. Veuillez réessayer.", "noMessages": "Aucun message pour le moment. Commencez une conversation!", "loadingMore": "Chargement de plus de messages..."}, "reasoning": {"title": "Étapes de Raisonnement", "step": "Étape", "confidence": "Confiance", "analysis": "Analyse", "planning": "Planification", "execution": "Exécution", "synthesis": "Synthèse", "conclusion": "Conclusion", "showDetails": "Afficher les Détails", "hideDetails": "Masquer les Détails"}, "tools": {"title": "<PERSON><PERSON>", "calculator": "Calculatrice", "webSearch": "Recherche Web", "codeAnalyzer": "Analyseur de Code", "dataAnalyzer": "Analy<PERSON><PERSON>", "summarizer": "R<PERSON><PERSON><PERSON>", "translator": "Traducteur", "imageAnalyzer": "Analyseur d'Images", "success": "Su<PERSON>ès", "failed": "Échec", "running": "En cours", "pending": "En attente"}, "files": {"upload": "Télécharger un Fichier", "dragDrop": "Glis<PERSON>z<PERSON><PERSON><PERSON><PERSON>z les fichiers ici ou cliquez pour parcourir", "supported": "Formats supportés", "maxSize": "<PERSON><PERSON> max", "processing": "Traitement...", "processed": "Traité", "failed": "Échec du traitement", "remove": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger", "preview": "<PERSON><PERSON><PERSON><PERSON>"}, "personality": {"title": "Personnalité IA", "tone": "Ton", "verbosity": "Verbosité", "creativity": "Créativité", "formality": "Formalité", "tones": {"friendly": "Amical", "professional": "Professionnel", "casual": "Décontracté", "enthusiastic": "Enthousias<PERSON>", "calm": "Calme"}, "verbosityLevels": {"brief": "<PERSON><PERSON><PERSON>", "normal": "Normal", "detailed": "Dé<PERSON>lé", "comprehensive": "Complet"}, "creativityLevels": {"conservative": "Conservateur", "balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creative": "<PERSON><PERSON><PERSON><PERSON>", "innovative": "Innovant"}, "formalityLevels": {"informal": "Informel", "neutral": "Neutre", "formal": "Formel", "academic": "Académique"}}, "settings": {"title": "Paramètres", "language": "<PERSON><PERSON>", "theme": "Thème", "notifications": "Notifications", "privacy": "Confidentialité", "advanced": "<PERSON><PERSON><PERSON>", "themes": {"light": "<PERSON>", "dark": "Sombre", "auto": "Auto"}, "save": "Enregistrer", "cancel": "Annuler", "reset": "Réinitialiser par Défaut", "export": "Exporter les Paramètres", "import": "Importer les Paramètres"}, "status": {"online": "En ligne", "offline": "<PERSON><PERSON> ligne", "connecting": "Connexion", "connected": "Connecté et Actif", "error": "<PERSON><PERSON><PERSON>", "maintenance": "En Maintenance"}, "common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "info": "Information", "confirm": "Confirmer", "cancel": "Annuler", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "previous": "Précédent", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "refresh": "Actualiser", "retry": "<PERSON><PERSON><PERSON><PERSON>", "more": "Plus", "less": "<PERSON>ins", "all": "<PERSON>ut", "none": "Aucun", "yes": "O<PERSON>", "no": "Non", "ok": "OK"}, "time": {"now": "Maintenant", "justNow": "À l'instant", "minuteAgo": "Il y a une minute", "minutesAgo": "Il y a {count} minutes", "hourAgo": "Il y a une heure", "hoursAgo": "Il y a {count} heures", "dayAgo": "Il y a un jour", "daysAgo": "Il y a {count} jours", "weekAgo": "Il y a une semaine", "weeksAgo": "Il y a {count} semaines", "monthAgo": "Il y a un mois", "monthsAgo": "Il y a {count} mois", "yearAgo": "Il y a un an", "yearsAgo": "Il y a {count} ans"}}