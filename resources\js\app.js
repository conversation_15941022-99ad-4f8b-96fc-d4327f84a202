import './bootstrap';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { i18n } from './i18n';

// Import global styles
import '../css/app.css';

/**
 * WIDDX AI v2.0 - Main Application Entry Point
 *
 * This file initializes the Vue application with all necessary plugins
 * and global configurations for the WIDDX AI system.
 */

// Create Pinia store
const pinia = createPinia();

// Simple test component first
const TestComponent = {
    template: `
        <div style="padding: 2rem; text-align: center;">
            <h2 style="color: #6366f1;">🎉 Vue.js is Working!</h2>
            <p>WIDDX AI v2.0 - Vue Application Loaded Successfully</p>
            <button @click="count++" style="padding: 0.5rem 1rem; background: #6366f1; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                Clicked {{ count }} times
            </button>
        </div>
    `,
    data() {
        return {
            count: 0
        }
    }
};

// Import components
import WiddxAIChat from './components/WiddxAIChat.vue';
import ChatMessage from './components/ChatMessage.vue';
import ModelBadge from './components/ModelBadge.vue';
import KnowledgeEntryModal from './components/KnowledgeEntryModal.vue';

// Create and configure the app
const app = createApp({
    template: `
        <div style="padding: 2rem; font-family: Inter, sans-serif; min-height: 100vh; background: #0f172a; color: #f1f5f9;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="font-size: 3rem; font-weight: 800; background: linear-gradient(135deg, #6366f1, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 0.5rem;">
                    🚀 WIDDX AI v2.0
                </h1>
                <p style="color: #94a3b8; font-size: 1.25rem; margin-bottom: 2rem;">Next Generation AI Assistant</p>

                <div style="background: rgba(99, 102, 241, 0.1); border: 1px solid rgba(99, 102, 241, 0.3); border-radius: 1rem; padding: 2rem; margin: 2rem auto; max-width: 600px;">
                    <h2 style="color: #6366f1; margin-bottom: 1rem;">🎉 Vue.js Application Loaded Successfully!</h2>
                    <p style="color: #94a3b8; margin-bottom: 1.5rem;">All systems are operational and ready to go.</p>

                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <button @click="count++" style="padding: 0.75rem 1.5rem; background: #6366f1; color: white; border: none; border-radius: 0.75rem; cursor: pointer; font-weight: 600;">
                            Clicked {{ count }} times
                        </button>

                        <button @click="showFullApp = !showFullApp" style="padding: 0.75rem 1.5rem; background: #8b5cf6; color: white; border: none; border-radius: 0.75rem; cursor: pointer; font-weight: 600;">
                            {{ showFullApp ? 'Hide' : 'Show' }} Full App
                        </button>
                    </div>
                </div>
            </div>

            <widdx-ai-chat v-if="showFullApp"></widdx-ai-chat>

            <div v-else style="text-align: center; padding: 2rem; background: rgba(255, 255, 255, 0.05); border-radius: 1rem; margin: 2rem auto; max-width: 800px;">
                <h3 style="color: #f1f5f9; margin-bottom: 1rem;">🔧 System Status</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                    <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.5rem; padding: 1rem;">
                        <div style="color: #10b981; font-weight: 600;">✅ Vue.js</div>
                        <div style="color: #94a3b8; font-size: 0.875rem;">Framework Loaded</div>
                    </div>
                    <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.5rem; padding: 1rem;">
                        <div style="color: #10b981; font-weight: 600;">✅ Pinia</div>
                        <div style="color: #94a3b8; font-size: 0.875rem;">State Management</div>
                    </div>
                    <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.5rem; padding: 1rem;">
                        <div style="color: #10b981; font-weight: 600;">✅ Vue I18n</div>
                        <div style="color: #94a3b8; font-size: 0.875rem;">Internationalization</div>
                    </div>
                    <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.5rem; padding: 1rem;">
                        <div style="color: #10b981; font-weight: 600;">✅ Components</div>
                        <div style="color: #94a3b8; font-size: 0.875rem;">All Loaded</div>
                    </div>
                </div>
                <p style="color: #94a3b8; margin-top: 1rem;">Ready to launch the full WIDDX AI experience!</p>
            </div>
        </div>
    `,
    data() {
        return {
            count: 0,
            showFullApp: false
        }
    },
    mounted() {
        console.log('🎉 WIDDX AI v2.0 - Vue app mounted successfully!');
        console.log('📊 All systems operational');

        // Hide loading screen
        setTimeout(() => {
            document.body.classList.add('vue-ready');
        }, 500);
    }
});

// Register global components
app.component('test-component', TestComponent);
app.component('widdx-ai-chat', WiddxAIChat);
app.component('chat-message', ChatMessage);
app.component('model-badge', ModelBadge);
app.component('knowledge-entry-modal', KnowledgeEntryModal);

// Use plugins
app.use(pinia);
app.use(i18n);

// Global properties
app.config.globalProperties.$csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

// Global error handler
app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err, info);

    // Send to error tracking service if available
    if (window.Sentry) {
        window.Sentry.captureException(err, {
            contexts: {
                vue: {
                    componentName: vm?.$options.name || 'Unknown',
                    propsData: vm?.$props,
                    info
                }
            }
        });
    }
};

// Performance monitoring
if (window.performance && window.performance.mark) {
    window.performance.mark('vue-app-init-start');
}

// Mount the application
app.mount('#app');

// Performance monitoring end
if (window.performance && window.performance.mark) {
    window.performance.mark('vue-app-init-end');
    window.performance.measure('vue-app-init', 'vue-app-init-start', 'vue-app-init-end');

    const measure = window.performance.getEntriesByName('vue-app-init')[0];
    console.log(`Vue app initialized in ${measure.duration.toFixed(2)}ms`);
}

// Global WIDDX AI object
window.WiddxAI = {
    version: '2.0.0',
    app,
    initialized: true,

    // Utility functions
    utils: {
        formatTime: (timestamp) => new Date(timestamp).toLocaleTimeString(),
        formatDate: (timestamp) => new Date(timestamp).toLocaleDateString(),

        copyToClipboard: async (text) => {
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (error) {
                console.error('Failed to copy:', error);
                return false;
            }
        },

        downloadFile: (content, filename, type = 'text/plain') => {
            const blob = new Blob([content], { type });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }
};

console.log('🚀 WIDDX AI v2.0 Application Loaded Successfully!');
console.log('🌟 Features: Multi-language, Advanced Chat, Chain of Thought, Tool Execution');
console.log('🔧 Developer tools available at window.WiddxAI');
