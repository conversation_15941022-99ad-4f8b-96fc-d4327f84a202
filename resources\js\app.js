import './bootstrap';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { i18n } from './i18n';

// Import global styles
import '../css/app.css';

/**
 * WIDDX AI v2.0 - Main Application Entry Point
 *
 * This file initializes the Vue application with all necessary plugins
 * and global configurations for the WIDDX AI system.
 */

// Create Pinia store
const pinia = createPinia();

// Import components
import WiddxAIChat from './components/WiddxAIChat.vue';
import ChatMessage from './components/ChatMessage.vue';
import ModelBadge from './components/ModelBadge.vue';
import KnowledgeEntryModal from './components/KnowledgeEntryModal.vue';

// Create and configure the app
const app = createApp({
    components: {
        WiddxAIChat,
        ChatMessage,
        ModelBadge,
        KnowledgeEntryModal
    }
});

// Use plugins
app.use(pinia);
app.use(i18n);

// Global properties
app.config.globalProperties.$csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

// Global error handler
app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err, info);

    // Send to error tracking service if available
    if (window.Sentry) {
        window.Sentry.captureException(err, {
            contexts: {
                vue: {
                    componentName: vm?.$options.name || 'Unknown',
                    propsData: vm?.$props,
                    info
                }
            }
        });
    }
};

// Performance monitoring
if (window.performance && window.performance.mark) {
    window.performance.mark('vue-app-init-start');
}

// Mount the application
app.mount('#app');

// Performance monitoring end
if (window.performance && window.performance.mark) {
    window.performance.mark('vue-app-init-end');
    window.performance.measure('vue-app-init', 'vue-app-init-start', 'vue-app-init-end');

    const measure = window.performance.getEntriesByName('vue-app-init')[0];
    console.log(`Vue app initialized in ${measure.duration.toFixed(2)}ms`);
}

// Global WIDDX AI object
window.WiddxAI = {
    version: '2.0.0',
    app,
    initialized: true,

    // Utility functions
    utils: {
        formatTime: (timestamp) => new Date(timestamp).toLocaleTimeString(),
        formatDate: (timestamp) => new Date(timestamp).toLocaleDateString(),

        copyToClipboard: async (text) => {
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (error) {
                console.error('Failed to copy:', error);
                return false;
            }
        },

        downloadFile: (content, filename, type = 'text/plain') => {
            const blob = new Blob([content], { type });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }
};

console.log('🚀 WIDDX AI v2.0 Application Loaded Successfully!');
console.log('🌟 Features: Multi-language, Advanced Chat, Chain of Thought, Tool Execution');
console.log('🔧 Developer tools available at window.WiddxAI');
