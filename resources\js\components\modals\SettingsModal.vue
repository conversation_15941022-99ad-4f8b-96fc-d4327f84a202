<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <!-- Modal Header -->
      <div class="modal-header">
        <h3>{{ $t('settings.title') }}</h3>
        <button class="modal-close" @click="$emit('close')" :title="$t('common.close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <!-- Modal Body -->
      <div class="modal-body">
        <!-- Language Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-language"></i>
            {{ $t('settings.language') }}
          </label>
          <select v-model="selectedLanguage" @change="changeLanguage" class="setting-select">
            <option v-for="lang in availableLanguages" :key="lang.code" :value="lang.code">
              {{ lang.flag }} {{ lang.nativeName }}
            </option>
          </select>
          <p class="setting-description">{{ $t('settings.languageDescription') }}</p>
        </div>
        
        <!-- Theme Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-palette"></i>
            {{ $t('settings.theme') }}
          </label>
          <div class="theme-options">
            <button 
              v-for="theme in themes" 
              :key="theme"
              class="theme-option"
              :class="{ 'active': currentTheme === theme }"
              @click="setTheme(theme)"
            >
              <i :class="getThemeIcon(theme)"></i>
              <span>{{ $t(`settings.themes.${theme}`) }}</span>
            </button>
          </div>
        </div>
        
        <!-- AI Personality Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-robot"></i>
            {{ $t('personality.title') }}
          </label>
          
          <!-- Tone -->
          <div class="personality-setting">
            <label class="personality-label">{{ $t('personality.tone') }}</label>
            <select v-model="personality.tone" class="setting-select">
              <option v-for="tone in tones" :key="tone" :value="tone">
                {{ $t(`personality.tones.${tone}`) }}
              </option>
            </select>
          </div>
          
          <!-- Verbosity -->
          <div class="personality-setting">
            <label class="personality-label">{{ $t('personality.verbosity') }}</label>
            <div class="slider-container">
              <input 
                type="range" 
                v-model="personality.verbosity" 
                min="1" 
                max="4" 
                step="1"
                class="setting-slider"
              >
              <div class="slider-labels">
                <span>{{ $t('personality.verbosityLevels.brief') }}</span>
                <span>{{ $t('personality.verbosityLevels.comprehensive') }}</span>
              </div>
            </div>
          </div>
          
          <!-- Creativity -->
          <div class="personality-setting">
            <label class="personality-label">{{ $t('personality.creativity') }}</label>
            <div class="slider-container">
              <input 
                type="range" 
                v-model="personality.creativity" 
                min="1" 
                max="4" 
                step="1"
                class="setting-slider"
              >
              <div class="slider-labels">
                <span>{{ $t('personality.creativityLevels.conservative') }}</span>
                <span>{{ $t('personality.creativityLevels.innovative') }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Display Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-eye"></i>
            {{ $t('settings.display') }}
          </label>
          
          <div class="checkbox-setting">
            <label class="checkbox-label">
              <input type="checkbox" v-model="displaySettings.showReasoningSteps">
              <span class="checkmark"></span>
              {{ $t('reasoning.showByDefault') }}
            </label>
          </div>
          
          <div class="checkbox-setting">
            <label class="checkbox-label">
              <input type="checkbox" v-model="displaySettings.showToolExecutions">
              <span class="checkmark"></span>
              {{ $t('tools.showByDefault') }}
            </label>
          </div>
          
          <div class="checkbox-setting">
            <label class="checkbox-label">
              <input type="checkbox" v-model="displaySettings.showConfidenceScores">
              <span class="checkmark"></span>
              {{ $t('settings.showConfidenceScores') }}
            </label>
          </div>
        </div>
        
        <!-- Privacy Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-shield-alt"></i>
            {{ $t('settings.privacy') }}
          </label>
          
          <div class="checkbox-setting">
            <label class="checkbox-label">
              <input type="checkbox" v-model="privacySettings.saveConversations">
              <span class="checkmark"></span>
              {{ $t('settings.saveConversations') }}
            </label>
          </div>
          
          <div class="checkbox-setting">
            <label class="checkbox-label">
              <input type="checkbox" v-model="privacySettings.allowAnalytics">
              <span class="checkmark"></span>
              {{ $t('settings.allowAnalytics') }}
            </label>
          </div>
        </div>
        
        <!-- Advanced Settings -->
        <div class="setting-group">
          <label class="setting-label">
            <i class="fas fa-cogs"></i>
            {{ $t('settings.advanced') }}
          </label>
          
          <div class="advanced-setting">
            <label class="advanced-label">{{ $t('settings.maxTokens') }}</label>
            <input 
              type="number" 
              v-model="advancedSettings.maxTokens" 
              min="100" 
              max="4000" 
              step="100"
              class="setting-input"
            >
          </div>
          
          <div class="advanced-setting">
            <label class="advanced-label">{{ $t('settings.temperature') }}</label>
            <div class="slider-container">
              <input 
                type="range" 
                v-model="advancedSettings.temperature" 
                min="0" 
                max="1" 
                step="0.1"
                class="setting-slider"
              >
              <div class="slider-value">{{ advancedSettings.temperature }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Modal Footer -->
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="resetSettings">
          <i class="fas fa-undo"></i>
          {{ $t('settings.reset') }}
        </button>
        
        <div class="footer-actions">
          <button class="btn btn-outline" @click="exportSettings">
            <i class="fas fa-download"></i>
            {{ $t('settings.export') }}
          </button>
          
          <label class="btn btn-outline">
            <input type="file" @change="importSettings" accept=".json" style="display: none;">
            <i class="fas fa-upload"></i>
            {{ $t('settings.import') }}
          </label>
          
          <button class="btn btn-primary" @click="saveSettings">
            <i class="fas fa-save"></i>
            {{ $t('settings.save') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '../../stores/app'
import { getAvailableLanguages, setLanguage, getCurrentLanguage } from '../../i18n'

// Emits
defineEmits(['close'])

// Composables
const { t, locale } = useI18n()
const appStore = useAppStore()

// Reactive state
const selectedLanguage = ref(getCurrentLanguage())
const currentTheme = ref(appStore.theme)

const personality = ref({
  tone: 'friendly',
  verbosity: 2,
  creativity: 2,
  formality: 2
})

const displaySettings = ref({
  showReasoningSteps: true,
  showToolExecutions: true,
  showConfidenceScores: true
})

const privacySettings = ref({
  saveConversations: true,
  allowAnalytics: false
})

const advancedSettings = ref({
  maxTokens: 2000,
  temperature: 0.7
})

// Computed
const availableLanguages = computed(() => getAvailableLanguages())

const themes = computed(() => ['light', 'dark', 'auto'])

const tones = computed(() => ['friendly', 'professional', 'casual', 'enthusiastic', 'calm'])

// Methods
const getThemeIcon = (theme) => {
  const icons = {
    light: 'fas fa-sun',
    dark: 'fas fa-moon',
    auto: 'fas fa-adjust'
  }
  return icons[theme] || 'fas fa-palette'
}

const changeLanguage = () => {
  appStore.setLanguageAndDirection(selectedLanguage.value)
  locale.value = selectedLanguage.value
}

const setTheme = (theme) => {
  currentTheme.value = theme
  appStore.setTheme(theme)
}

const saveSettings = () => {
  try {
    // Save all settings to localStorage
    const settings = {
      language: selectedLanguage.value,
      theme: currentTheme.value,
      personality: personality.value,
      display: displaySettings.value,
      privacy: privacySettings.value,
      advanced: advancedSettings.value,
      savedAt: new Date().toISOString()
    }
    
    localStorage.setItem('widdx-ai-settings', JSON.stringify(settings))
    
    // Apply settings
    changeLanguage()
    setTheme(currentTheme.value)
    
    // Show success message
    console.log('Settings saved successfully')
    
    // Close modal
    $emit('close')
  } catch (error) {
    console.error('Failed to save settings:', error)
  }
}

const resetSettings = () => {
  if (confirm(t('settings.confirmReset'))) {
    selectedLanguage.value = 'en'
    currentTheme.value = 'dark'
    personality.value = {
      tone: 'friendly',
      verbosity: 2,
      creativity: 2,
      formality: 2
    }
    displaySettings.value = {
      showReasoningSteps: true,
      showToolExecutions: true,
      showConfidenceScores: true
    }
    privacySettings.value = {
      saveConversations: true,
      allowAnalytics: false
    }
    advancedSettings.value = {
      maxTokens: 2000,
      temperature: 0.7
    }
    
    // Clear saved settings
    localStorage.removeItem('widdx-ai-settings')
  }
}

const exportSettings = () => {
  try {
    const settings = {
      language: selectedLanguage.value,
      theme: currentTheme.value,
      personality: personality.value,
      display: displaySettings.value,
      privacy: privacySettings.value,
      advanced: advancedSettings.value,
      exportedAt: new Date().toISOString(),
      version: '2.0.0'
    }
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `widdx-ai-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export settings:', error)
  }
}

const importSettings = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const settings = JSON.parse(e.target.result)
      
      // Validate and apply settings
      if (settings.language) selectedLanguage.value = settings.language
      if (settings.theme) currentTheme.value = settings.theme
      if (settings.personality) personality.value = { ...personality.value, ...settings.personality }
      if (settings.display) displaySettings.value = { ...displaySettings.value, ...settings.display }
      if (settings.privacy) privacySettings.value = { ...privacySettings.value, ...settings.privacy }
      if (settings.advanced) advancedSettings.value = { ...advancedSettings.value, ...settings.advanced }
      
      console.log('Settings imported successfully')
    } catch (error) {
      console.error('Failed to import settings:', error)
      alert(t('settings.importError'))
    }
  }
  reader.readAsText(file)
  
  // Clear input
  event.target.value = ''
}

const loadSettings = () => {
  try {
    const saved = localStorage.getItem('widdx-ai-settings')
    if (saved) {
      const settings = JSON.parse(saved)
      
      if (settings.language) selectedLanguage.value = settings.language
      if (settings.theme) currentTheme.value = settings.theme
      if (settings.personality) personality.value = { ...personality.value, ...settings.personality }
      if (settings.display) displaySettings.value = { ...displaySettings.value, ...settings.display }
      if (settings.privacy) privacySettings.value = { ...privacySettings.value, ...settings.privacy }
      if (settings.advanced) advancedSettings.value = { ...advancedSettings.value, ...settings.advanced }
    }
  } catch (error) {
    console.error('Failed to load settings:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.setting-group {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.setting-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.setting-label i {
  color: var(--primary-500);
}

.setting-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.setting-select,
.setting-input {
  width: 100%;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.theme-option {
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.theme-option:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-500);
}

.theme-option.active {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.theme-option i {
  font-size: 1.25rem;
}

.personality-setting,
.advanced-setting {
  margin-bottom: 1rem;
}

.personality-label,
.advanced-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.slider-container {
  position: relative;
}

.setting-slider {
  width: 100%;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.setting-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-500);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.setting-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-500);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.slider-value {
  position: absolute;
  top: -2rem;
  right: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.checkbox-setting {
  margin-bottom: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 0.375rem;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.btn-outline {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .theme-options {
    grid-template-columns: 1fr;
  }
  
  .footer-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
