<template>
  <div class="chat-header">
    <!-- Mobile Sidebar Toggle -->
    <button 
      class="sidebar-toggle" 
      @click="appStore.toggleSidebar" 
      v-if="isMobile"
      :title="$t('navigation.conversations')"
    >
      <i class="fas fa-bars"></i>
    </button>
    
    <!-- Chat Title -->
    <div class="chat-title">
      <h1>{{ chatStore.conversationTitle }}</h1>
      <div class="ai-status">
        <div class="status-indicator" :class="statusClass"></div>
        <span>{{ statusText }}</span>
      </div>
    </div>
    
    <!-- Chat Controls -->
    <div class="chat-controls">
      <!-- Reasoning Steps Toggle -->
      <button 
        class="control-btn" 
        @click="toggleReasoningSteps" 
        :class="{ 'active': showReasoningSteps }"
        :title="$t('reasoning.title')"
      >
        <i class="fas fa-brain"></i>
      </button>
      
      <!-- Language Toggle -->
      <button 
        class="control-btn" 
        @click="toggleLanguage"
        :title="$t('settings.language')"
      >
        <i class="fas fa-language"></i>
        <span class="language-indicator">{{ currentLanguageFlag }}</span>
      </button>
      
      <!-- Theme Toggle -->
      <button 
        class="control-btn" 
        @click="appStore.toggleTheme"
        :title="$t('settings.theme')"
      >
        <i :class="appStore.isDark ? 'fas fa-sun' : 'fas fa-moon'"></i>
      </button>
      
      <!-- Settings -->
      <button 
        class="control-btn" 
        @click="$emit('open-settings')"
        :title="$t('settings.title')"
      >
        <i class="fas fa-cog"></i>
      </button>
      
      <!-- Export Chat -->
      <button 
        class="control-btn" 
        @click="exportChat"
        :title="$t('chat.export')"
        v-if="chatStore.hasMessages"
      >
        <i class="fas fa-download"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '../../stores/app'
import { useChatStore } from '../../stores/chat'
import { getAvailableLanguages, getCurrentLanguage } from '../../i18n'

// Emits
defineEmits(['open-settings'])

// Composables
const { t, locale } = useI18n()
const appStore = useAppStore()
const chatStore = useChatStore()

// Reactive state
const showReasoningSteps = ref(true)
const connectionStatus = ref('connected')
const isMobile = ref(false)

// Computed
const availableLanguages = computed(() => getAvailableLanguages())

const currentLanguageFlag = computed(() => {
  const currentLang = getCurrentLanguage()
  const language = availableLanguages.value.find(lang => lang.code === currentLang)
  return language?.flag || '🌐'
})

const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'online'
    case 'connecting': return 'connecting'
    case 'error': return 'error'
    default: return 'offline'
  }
})

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return t('status.connected')
    case 'connecting': return t('status.connecting')
    case 'error': return t('status.error')
    default: return t('status.offline')
  }
})

// Methods
const toggleReasoningSteps = () => {
  showReasoningSteps.value = !showReasoningSteps.value
  // Emit event to parent component
  window.dispatchEvent(new CustomEvent('toggle-reasoning-steps', {
    detail: { show: showReasoningSteps.value }
  }))
}

const toggleLanguage = () => {
  const languages = availableLanguages.value
  const currentIndex = languages.findIndex(lang => lang.code === getCurrentLanguage())
  const nextIndex = (currentIndex + 1) % languages.length
  const nextLanguage = languages[nextIndex].code
  
  appStore.setLanguageAndDirection(nextLanguage)
  locale.value = nextLanguage
}

const exportChat = async () => {
  try {
    const messages = chatStore.messages
    const conversation = chatStore.currentConversation
    
    if (!messages.length) return
    
    const exportData = {
      conversation: {
        id: conversation?.id,
        title: conversation?.title || 'WIDDX AI Chat',
        created_at: conversation?.created_at || new Date().toISOString(),
        exported_at: new Date().toISOString()
      },
      messages: messages.map(message => ({
        role: message.role,
        content: message.content,
        timestamp: message.timestamp,
        reasoning_steps: message.reasoning_steps || [],
        tool_executions: message.tool_executions || [],
        confidence_score: message.confidence_score
      })),
      metadata: {
        version: '2.0.0',
        total_messages: messages.length,
        export_format: 'json'
      }
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `widdx-ai-chat-${conversation?.id || 'export'}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    // Show success message
    console.log('Chat exported successfully')
  } catch (error) {
    console.error('Failed to export chat:', error)
  }
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const updateConnectionStatus = () => {
  connectionStatus.value = navigator.onLine ? 'connected' : 'offline'
}

// Lifecycle
onMounted(() => {
  checkMobile()
  updateConnectionStatus()
  
  window.addEventListener('resize', checkMobile)
  window.addEventListener('online', updateConnectionStatus)
  window.addEventListener('offline', updateConnectionStatus)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  window.removeEventListener('online', updateConnectionStatus)
  window.removeEventListener('offline', updateConnectionStatus)
})
</script>

<style scoped>
.chat-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: none;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1rem;
}

.sidebar-toggle:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.chat-title h1 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-indicator.online {
  background: var(--success-500);
  animation: pulse 2s infinite;
}

.status-indicator.connecting {
  background: var(--warning-500);
  animation: pulse 1s infinite;
}

.status-indicator.error {
  background: var(--error-500);
  animation: pulse 0.5s infinite;
}

.status-indicator.offline {
  background: var(--text-tertiary);
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
}

.control-btn:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.control-btn.active {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.control-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary-500);
  border-radius: 50%;
}

.language-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  font-size: 0.625rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .sidebar-toggle {
    display: flex;
  }
  
  .chat-header {
    padding: 0.75rem 1rem;
  }
  
  .chat-title h1 {
    font-size: 1rem;
    max-width: 200px;
  }
  
  .chat-controls {
    gap: 0.25rem;
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 0.5rem 0.75rem;
  }
  
  .chat-title h1 {
    font-size: 0.875rem;
    max-width: 150px;
  }
  
  .ai-status {
    font-size: 0.75rem;
  }
  
  .control-btn {
    width: 32px;
    height: 32px;
    font-size: 0.625rem;
  }
  
  .language-indicator {
    width: 14px;
    height: 14px;
    font-size: 0.5rem;
  }
}
</style>
