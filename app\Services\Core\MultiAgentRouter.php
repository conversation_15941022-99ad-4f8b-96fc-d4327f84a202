<?php

namespace App\Services\Core;

use App\Models\ModelPerformance;
use App\Services\LLM\DeepSeekService;
use App\Services\LLM\GeminiService;
use App\Services\LLM\HuggingFaceService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Advanced Multi-Agent Router with Dynamic Model Selection
 * Routes requests to optimal models based on task type, performance history, and context
 */
class MultiAgentRouter
{
    private array $models;
    private array $taskClassifiers;

    public function __construct()
    {
        $this->models = [
            'deepseek' => app(DeepSeekService::class),
            'gemini' => app(GeminiService::class),
            'huggingface' => app(HuggingFaceService::class)
        ];

        $this->taskClassifiers = [
            'coding' => ['code', 'programming', 'function', 'debug', 'algorithm', 'كود', 'برمجة'],
            'research' => ['research', 'explain', 'analyze', 'study', 'بحث', 'تحليل', 'دراسة'],
            'creative' => ['creative', 'story', 'write', 'generate', 'إبداعي', 'قصة', 'اكتب'],
            'analysis' => ['data', 'statistics', 'chart', 'analyze', 'بيانات', 'إحصائيات', 'تحليل'],
            'general' => ['help', 'question', 'what', 'how', 'مساعدة', 'سؤال', 'ما', 'كيف']
        ];
    }

    /**
     * Route request to optimal model(s) with fallback and voting
     */
    public function route(string $message, array $context = []): array
    {
        $taskType = $this->classifyTask($message);
        $inputCharacteristics = $this->analyzeInput($message, $context);
        
        // Get optimal model selection strategy
        $strategy = $this->getRoutingStrategy($taskType, $inputCharacteristics);
        
        Log::info('MultiAgentRouter: Routing decision', [
            'task_type' => $taskType,
            'strategy' => $strategy,
            'input_chars' => $inputCharacteristics
        ]);

        return match($strategy['type']) {
            'single' => $this->singleModelResponse($strategy['model'], $message, $context),
            'fallback' => $this->fallbackResponse($strategy['models'], $message, $context),
            'voting' => $this->votingResponse($strategy['models'], $message, $context),
            'collaborative' => $this->collaborativeResponse($strategy['models'], $message, $context),
            default => $this->singleModelResponse('deepseek', $message, $context)
        };
    }

    /**
     * Classify task type based on message content
     */
    private function classifyTask(string $message): string
    {
        $message = strtolower($message);
        $scores = [];

        foreach ($this->taskClassifiers as $taskType => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                if (strpos($message, strtolower($keyword)) !== false) {
                    $score += 1;
                }
            }
            $scores[$taskType] = $score;
        }

        // Return task type with highest score, default to 'general'
        return array_key_exists(max($scores), array_flip($scores)) 
            ? array_flip($scores)[max($scores)] 
            : 'general';
    }

    /**
     * Analyze input characteristics for routing decisions
     */
    private function analyzeInput(string $message, array $context): array
    {
        return [
            'length' => strlen($message),
            'complexity' => $this->assessComplexity($message),
            'language' => $this->detectLanguage($message),
            'has_code' => $this->hasCodeSnippets($message),
            'question_count' => substr_count($message, '?'),
            'context_size' => count($context)
        ];
    }

    /**
     * Determine routing strategy based on task and performance data
     */
    private function getRoutingStrategy(string $taskType, array $inputCharacteristics): array
    {
        $performances = $this->getModelPerformances($taskType);
        
        // High complexity or critical tasks use voting
        if ($inputCharacteristics['complexity'] === 'high' || $inputCharacteristics['length'] > 1000) {
            return [
                'type' => 'voting',
                'models' => $this->selectTopModels($performances, 2)
            ];
        }

        // Medium complexity uses fallback
        if ($inputCharacteristics['complexity'] === 'medium') {
            return [
                'type' => 'fallback',
                'models' => $this->selectTopModels($performances, 2)
            ];
        }

        // Simple tasks use single best model
        $bestModel = $this->selectTopModels($performances, 1)[0] ?? 'deepseek';
        return [
            'type' => 'single',
            'model' => $bestModel
        ];
    }

    /**
     * Single model response
     */
    private function singleModelResponse(string $modelName, string $message, array $context): array
    {
        $startTime = microtime(true);
        
        try {
            $response = $this->models[$modelName]->generateResponse($message, $context);
            $responseTime = microtime(true) - $startTime;
            
            $this->updateModelPerformance($modelName, true, $responseTime, $context);
            
            return [
                'strategy' => 'single',
                'primary_model' => $modelName,
                'response' => $response,
                'metadata' => [
                    'response_time' => $responseTime,
                    'model_used' => $modelName
                ]
            ];
        } catch (\Exception $e) {
            $this->updateModelPerformance($modelName, false, microtime(true) - $startTime, $context);
            throw $e;
        }
    }

    /**
     * Fallback response - try models in order until success
     */
    private function fallbackResponse(array $modelNames, string $message, array $context): array
    {
        $attempts = [];
        
        foreach ($modelNames as $modelName) {
            $startTime = microtime(true);
            
            try {
                $response = $this->models[$modelName]->generateResponse($message, $context);
                $responseTime = microtime(true) - $startTime;
                
                $this->updateModelPerformance($modelName, true, $responseTime, $context);
                
                return [
                    'strategy' => 'fallback',
                    'primary_model' => $modelName,
                    'response' => $response,
                    'attempts' => array_merge($attempts, [$modelName]),
                    'metadata' => [
                        'response_time' => $responseTime,
                        'fallback_used' => count($attempts) > 0
                    ]
                ];
            } catch (\Exception $e) {
                $attempts[] = $modelName;
                $this->updateModelPerformance($modelName, false, microtime(true) - $startTime, $context);
                Log::warning("Model {$modelName} failed, trying fallback", ['error' => $e->getMessage()]);
            }
        }
        
        throw new \Exception('All models failed to generate response');
    }

    /**
     * Voting response - get responses from multiple models and select best
     */
    private function votingResponse(array $modelNames, string $message, array $context): array
    {
        $responses = [];
        $totalTime = 0;
        
        foreach ($modelNames as $modelName) {
            $startTime = microtime(true);
            
            try {
                $response = $this->models[$modelName]->generateResponse($message, $context);
                $responseTime = microtime(true) - $startTime;
                $totalTime += $responseTime;
                
                $responses[$modelName] = [
                    'content' => $response,
                    'response_time' => $responseTime,
                    'confidence' => $this->calculateConfidence($response, $context)
                ];
                
                $this->updateModelPerformance($modelName, true, $responseTime, $context);
            } catch (\Exception $e) {
                $this->updateModelPerformance($modelName, false, microtime(true) - $startTime, $context);
                Log::warning("Model {$modelName} failed in voting", ['error' => $e->getMessage()]);
            }
        }
        
        if (empty($responses)) {
            throw new \Exception('No models provided valid responses for voting');
        }
        
        // Select best response based on confidence and other factors
        $bestModel = $this->selectBestResponse($responses);
        
        return [
            'strategy' => 'voting',
            'primary_model' => $bestModel,
            'response' => $responses[$bestModel]['content'],
            'all_responses' => $responses,
            'metadata' => [
                'total_response_time' => $totalTime,
                'models_used' => array_keys($responses),
                'confidence_scores' => array_map(fn($r) => $r['confidence'], $responses)
            ]
        ];
    }

    /**
     * Collaborative response - combine insights from multiple models
     */
    private function collaborativeResponse(array $modelNames, string $message, array $context): array
    {
        $responses = [];
        
        foreach ($modelNames as $modelName) {
            try {
                $response = $this->models[$modelName]->generateResponse($message, $context);
                $responses[$modelName] = $response;
            } catch (\Exception $e) {
                Log::warning("Model {$modelName} failed in collaboration", ['error' => $e->getMessage()]);
            }
        }
        
        // Synthesize responses into collaborative answer
        $synthesizedResponse = $this->synthesizeResponses($responses, $message);
        
        return [
            'strategy' => 'collaborative',
            'response' => $synthesizedResponse,
            'individual_responses' => $responses,
            'metadata' => [
                'models_used' => array_keys($responses),
                'synthesis_method' => 'weighted_combination'
            ]
        ];
    }

    // Additional helper methods would continue here...
    
    private function assessComplexity(string $message): string
    {
        $wordCount = str_word_count($message);
        $hasSpecialChars = preg_match('/[{}()\[\]<>]/', $message);
        $hasMultipleQuestions = substr_count($message, '?') > 1;
        
        if ($wordCount > 100 || $hasSpecialChars || $hasMultipleQuestions) {
            return 'high';
        } elseif ($wordCount > 30) {
            return 'medium';
        }
        
        return 'low';
    }

    private function detectLanguage(string $message): string
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $message) ? 'ar' : 'en';
    }

    private function hasCodeSnippets(string $message): bool
    {
        return preg_match('/```|function|class|def |import |#include/', $message);
    }
}
