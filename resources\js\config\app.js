/**
 * WIDDX AI v2.0 - Application Configuration
 * 
 * This file contains all the configuration settings for the WIDDX AI frontend application.
 * Modify these settings to customize the behavior and appearance of your AI assistant.
 */

export default {
  // Application Information
  app: {
    name: 'WIDDX AI',
    version: '2.0.0',
    description: 'Next Generation AI Assistant with Advanced Multi-Modal Capabilities',
    author: 'WIDDX AI Team',
    website: 'https://widdx.ai',
    repository: 'https://github.com/your-username/widdx-ai',
  },

  // Default Settings
  defaults: {
    language: 'en',
    theme: 'dark',
    direction: 'ltr',
    timezone: 'UTC',
  },

  // Feature Flags
  features: {
    // Core Features
    multiLanguage: true,
    darkMode: true,
    rtlSupport: true,
    
    // Chat Features
    voiceInput: true,
    fileUpload: true,
    emojiPicker: true,
    messageActions: true,
    conversationExport: true,
    
    // AI Features
    reasoningSteps: true,
    toolExecution: true,
    confidenceScores: true,
    modelSelection: true,
    chainOfThought: true,
    
    // Advanced Features
    adminPanel: true,
    apiAccess: true,
    webhooks: false,
    analytics: false,
    
    // Experimental Features
    voiceOutput: false,
    videoChat: false,
    screenSharing: false,
  },

  // UI Configuration
  ui: {
    // Layout
    sidebar: {
      defaultOpen: true,
      width: 320,
      collapsible: true,
      resizable: false,
    },
    
    // Chat Interface
    chat: {
      maxMessageLength: 4000,
      typingIndicator: true,
      readReceipts: false,
      messageTimestamps: true,
      avatars: true,
      bubbleStyle: 'modern', // 'classic', 'modern', 'minimal'
    },
    
    // Animations
    animations: {
      enabled: true,
      duration: 300,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      reducedMotion: false, // Respect user's motion preferences
    },
    
    // Responsive Breakpoints
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1280,
      wide: 1536,
    },
  },

  // AI Configuration
  ai: {
    // Default Model
    defaultModel: 'deepseek',
    
    // Model Preferences
    models: {
      deepseek: {
        name: 'DeepSeek',
        description: 'Advanced reasoning and coding',
        icon: 'fas fa-brain',
        color: '#6366f1',
        maxTokens: 4000,
        temperature: 0.7,
        enabled: true,
      },
      gemini: {
        name: 'Gemini',
        description: 'Multi-modal AI processing',
        icon: 'fas fa-gem',
        color: '#8b5cf6',
        maxTokens: 2048,
        temperature: 0.8,
        enabled: true,
      },
      huggingface: {
        name: 'HuggingFace',
        description: 'Open-source models',
        icon: 'fas fa-robot',
        color: '#06b6d4',
        maxTokens: 2048,
        temperature: 0.9,
        enabled: true,
      },
    },
    
    // Personality Settings
    personality: {
      tone: 'friendly', // 'professional', 'casual', 'enthusiastic', 'calm'
      verbosity: 2, // 1-4 scale
      creativity: 2, // 1-4 scale
      formality: 2, // 1-4 scale
    },
    
    // Response Settings
    responses: {
      showReasoningSteps: true,
      showToolExecutions: true,
      showConfidenceScores: true,
      autoSaveConversations: true,
      contextWindow: 10, // Number of previous messages to include
    },
  },

  // File Upload Configuration
  files: {
    // Allowed file types
    allowedTypes: [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // Text files
      'text/plain',
      'text/csv',
      'text/html',
      'text/css',
      'text/javascript',
      'application/json',
      'application/xml',
      'text/markdown',
      
      // Code files
      'text/x-python',
      'text/x-java',
      'text/x-c',
      'text/x-cpp',
      'text/x-csharp',
      'text/x-php',
      'text/x-ruby',
      'text/x-go',
      'text/x-rust',
      
      // Archives
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
    ],
    
    // File size limits (in bytes)
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    
    // Preview settings
    preview: {
      images: true,
      documents: true,
      text: true,
      code: true,
    },
  },

  // Internationalization
  i18n: {
    // Available languages
    languages: [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        flag: '🇺🇸',
        rtl: false,
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
        rtl: true,
      },
      {
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        flag: '🇫🇷',
        rtl: false,
      },
      {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        flag: '🇪🇸',
        rtl: false,
      },
      {
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        flag: '🇩🇪',
        rtl: false,
      },
    ],
    
    // Fallback language
    fallback: 'en',
    
    // Date/time formatting
    dateFormat: {
      short: 'MM/DD/YYYY',
      long: 'MMMM DD, YYYY',
      time: 'HH:mm',
      dateTime: 'MM/DD/YYYY HH:mm',
    },
  },

  // API Configuration
  api: {
    baseURL: '/api/v2',
    timeout: 30000, // 30 seconds
    retries: 3,
    retryDelay: 1000, // 1 second
    
    // Endpoints
    endpoints: {
      chat: '/chat',
      conversations: '/conversations',
      messages: '/messages',
      files: '/files',
      settings: '/settings',
      admin: '/admin',
    },
  },

  // Storage Configuration
  storage: {
    // Local storage keys
    keys: {
      theme: 'widdx-theme',
      language: 'widdx-language',
      settings: 'widdx-settings',
      conversations: 'widdx-conversations',
      drafts: 'widdx-drafts',
    },
    
    // Cache settings
    cache: {
      conversations: 24 * 60 * 60 * 1000, // 24 hours
      messages: 60 * 60 * 1000, // 1 hour
      settings: 7 * 24 * 60 * 60 * 1000, // 7 days
    },
  },

  // Performance Configuration
  performance: {
    // Lazy loading
    lazyLoading: true,
    
    // Virtual scrolling for large lists
    virtualScrolling: true,
    virtualScrollThreshold: 100,
    
    // Debounce settings
    debounce: {
      search: 300,
      input: 150,
      resize: 100,
    },
    
    // Throttle settings
    throttle: {
      scroll: 16, // ~60fps
      api: 1000,
    },
  },

  // Security Configuration
  security: {
    // Content Security Policy
    csp: {
      enabled: true,
      reportOnly: false,
    },
    
    // XSS Protection
    xssProtection: true,
    
    // CSRF Protection
    csrfProtection: true,
    
    // Sanitization
    sanitizeInput: true,
    sanitizeOutput: true,
  },

  // Development Configuration
  development: {
    // Debug mode
    debug: process.env.NODE_ENV === 'development',
    
    // Logging
    logging: {
      level: 'info', // 'error', 'warn', 'info', 'debug'
      console: true,
      remote: false,
    },
    
    // Hot reloading
    hotReload: true,
    
    // Source maps
    sourceMaps: true,
  },

  // Analytics Configuration
  analytics: {
    enabled: false,
    provider: null, // 'google', 'mixpanel', 'amplitude'
    trackingId: null,
    
    // Events to track
    events: {
      pageViews: true,
      userActions: true,
      errors: true,
      performance: false,
    },
  },

  // Accessibility Configuration
  accessibility: {
    // ARIA labels
    ariaLabels: true,
    
    // Keyboard navigation
    keyboardNavigation: true,
    
    // Screen reader support
    screenReader: true,
    
    // High contrast mode
    highContrast: false,
    
    // Focus management
    focusManagement: true,
  },
};
