<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class AIService
{
    protected $services = [
        'deepseek' => DeepSeekService::class,
        'gemini' => GeminiService::class,
        'huggingface' => HuggingFaceService::class
    ];

    /**
     * Generate AI response
     */
    public function generateResponse(array $params): array
    {
        $startTime = microtime(true);

        try {
            // Detect user language
            $detectedLanguage = $this->detectLanguage($params['message']);

            // Determine which service to use
            $service = $this->selectService($params);

            // Generate response with language context
            $response = $this->callService($service, $params, $detectedLanguage);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'content' => $response['content'] ?? $this->getFallbackMessage($detectedLanguage),
                'model' => $response['model'] ?? $service,
                'service' => $service,
                'language' => $detectedLanguage,
                'tokens_used' => $response['tokens_used'] ?? 0,
                'processing_time' => $processingTime
            ];

        } catch (\Exception $e) {
            Log::error('AI Service error: ' . $e->getMessage());

            // Detect language for error message
            $errorLanguage = $this->detectLanguage($params['message'] ?? '');

            return [
                'content' => $this->getErrorMessage($errorLanguage),
                'model' => 'fallback',
                'service' => 'fallback',
                'language' => $errorLanguage,
                'tokens_used' => 0,
                'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * Select appropriate AI service
     */
    protected function selectService(array $params): string
    {
        // If preferred service is specified and available (but not 'auto')
        if (!empty($params['preferred_service']) &&
            $params['preferred_service'] !== 'auto' &&
            isset($this->services[$params['preferred_service']])) {
            return $params['preferred_service'];
        }

        // Auto-select based on message content
        $message = strtolower($params['message']);

        // Code-related queries -> DeepSeek
        if (preg_match('/\b(code|programming|function|class|variable|algorithm|debug|syntax|php|javascript|python|java|css|html|sql)\b/', $message)) {
            return 'deepseek';
        }

        // Creative or general queries -> Gemini
        if (preg_match('/\b(creative|story|poem|explain|help|what|how|why|write|create|generate)\b/', $message)) {
            return 'gemini';
        }

        // Technical or analysis queries -> HuggingFace
        if (preg_match('/\b(analyze|analysis|research|data|science|machine learning|ai|model)\b/', $message)) {
            return 'huggingface';
        }

        // Default to DeepSeek
        return 'deepseek';
    }

    /**
     * Call specific AI service
     */
    protected function callService(string $serviceName, array $params, string $language = 'en'): array
    {
        // For now, return a mock response since we don't have API keys configured
        return $this->getMockResponse($serviceName, $params['message'], $language);
    }

    /**
     * Detect language from user message
     */
    protected function detectLanguage(string $message): string
    {
        // Arabic detection
        if (preg_match('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}]/u', $message)) {
            return 'ar';
        }

        // French detection
        if (preg_match('/\b(je|tu|il|elle|nous|vous|ils|elles|le|la|les|un|une|des|du|de|et|ou|mais|donc|car|ni|bonjour|merci|au revoir)\b/i', $message)) {
            return 'fr';
        }

        // Spanish detection
        if (preg_match('/\b(yo|tú|él|ella|nosotros|vosotros|ellos|ellas|el|la|los|las|un|una|unos|unas|y|o|pero|hola|gracias|adiós)\b/i', $message)) {
            return 'es';
        }

        // German detection
        if (preg_match('/\b(ich|du|er|sie|es|wir|ihr|sie|der|die|das|ein|eine|und|oder|aber|hallo|danke|auf wiedersehen)\b/i', $message)) {
            return 'de';
        }

        // Chinese detection (simplified)
        if (preg_match('/[\x{4e00}-\x{9fff}]/u', $message)) {
            return 'zh';
        }

        // Japanese detection
        if (preg_match('/[\x{3040}-\x{309f}\x{30a0}-\x{30ff}\x{4e00}-\x{9faf}]/u', $message)) {
            return 'ja';
        }

        // Russian detection
        if (preg_match('/[\x{0400}-\x{04FF}]/u', $message)) {
            return 'ru';
        }

        // Default to English
        return 'en';
    }

    /**
     * Get fallback message in user's language
     */
    protected function getFallbackMessage(string $language): string
    {
        $messages = [
            'ar' => 'عذراً، لم أتمكن من توليد رد. يرجى المحاولة مرة أخرى.',
            'fr' => 'Désolé, je n\'ai pas pu générer une réponse. Veuillez réessayer.',
            'es' => 'Lo siento, no pude generar una respuesta. Por favor, inténtalo de nuevo.',
            'de' => 'Entschuldigung, ich konnte keine Antwort generieren. Bitte versuchen Sie es erneut.',
            'zh' => '抱歉，我无法生成回复。请重试。',
            'ja' => '申し訳ございませんが、回答を生成できませんでした。もう一度お試しください。',
            'ru' => 'Извините, я не смог сгенерировать ответ. Пожалуйста, попробуйте еще раз.',
            'en' => 'Sorry, I could not generate a response. Please try again.'
        ];

        return $messages[$language] ?? $messages['en'];
    }

    /**
     * Get error message in user's language
     */
    protected function getErrorMessage(string $language): string
    {
        $messages = [
            'ar' => 'أعتذر، واجهت خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
            'fr' => 'Je m\'excuse, j\'ai rencontré une erreur lors du traitement de votre demande. Veuillez réessayer.',
            'es' => 'Me disculpo, encontré un error al procesar su solicitud. Por favor, inténtelo de nuevo.',
            'de' => 'Entschuldigung, ich bin auf einen Fehler bei der Bearbeitung Ihrer Anfrage gestoßen. Bitte versuchen Sie es erneut.',
            'zh' => '抱歉，处理您的请求时遇到错误。请重试。',
            'ja' => '申し訳ございませんが、リクエストの処理中にエラーが発生しました。もう一度お試しください。',
            'ru' => 'Извините, я столкнулся с ошибкой при обработке вашего запроса. Пожалуйста, попробуйте еще раз.',
            'en' => 'I apologize, but I encountered an error while processing your request. Please try again.'
        ];

        return $messages[$language] ?? $messages['en'];
    }

    /**
     * Get mock response for testing
     */
    protected function getMockResponse(string $service, string $message, string $language = 'en'): array
    {
        // Generate more realistic responses based on message content
        $messageWords = str_word_count(strtolower($message));
        $isQuestion = str_contains($message, '?');
        $isGreeting = preg_match('/\b(hello|hi|hey|greetings)\b/i', $message);

        if ($isGreeting) {
            $responses = $this->getGreetingResponses($service, $language);
        } else {
            // Generate contextual responses
            $responses = [
                'deepseek' => [
                    'content' => $this->generateDeepSeekResponse($message, $language),
                    'model' => 'deepseek-chat',
                    'tokens_used' => rand(100, 200)
                ],
                'gemini' => [
                    'content' => $this->generateGeminiResponse($message, $language),
                    'model' => 'gemini-pro',
                    'tokens_used' => rand(90, 180)
                ],
                'huggingface' => [
                    'content' => $this->generateHuggingFaceResponse($message, $language),
                    'model' => 'huggingface-inference',
                    'tokens_used' => rand(80, 160)
                ]
            ];
        }

        return $responses[$service] ?? $responses['deepseek'];
    }

    /**
     * Get greeting responses in different languages
     */
    protected function getGreetingResponses(string $service, string $language): array
    {
        $greetings = [
            'ar' => [
                'deepseek' => [
                    'content' => "مرحباً! أنا WIDDX AI مدعوم بـ **DeepSeek**. سعيد بلقائك! 👋\n\nأنا متخصص بشكل خاص في:\n- **توليد الكود** وإصلاح الأخطاء\n- **حل المشاكل التقنية**\n- **التفكير الرياضي**\n- **مناقشات هندسة الأنظمة**\n\nما التحدي التقني الذي يمكنني مساعدتك فيه اليوم؟",
                    'model' => 'deepseek-chat',
                    'tokens_used' => rand(80, 120)
                ],
                'gemini' => [
                    'content' => "أهلاً وسهلاً! أنا WIDDX AI باستخدام **Gemini من Google**. سعيد بالتواصل معك! ✨\n\nأتميز في:\n- **الكتابة الإبداعية** والعصف الذهني\n- **البحث** وتجميع المعلومات\n- **الشرح** بطريقة مبسطة\n- **المحادثة العامة** والنصائح\n\nماذا تود أن نستكشف أو نناقش؟",
                    'model' => 'gemini-pro',
                    'tokens_used' => rand(70, 110)
                ],
                'huggingface' => [
                    'content' => "تحياتي! أنا WIDDX AI باستخدام نماذج **HuggingFace**. أهلاً بك! 🤗\n\nأعمل بقوة المجتمع مفتوح المصدر وأتخصص في:\n- **تحليل النصوص** ومعالجتها\n- **دعم متعدد اللغات**\n- **تحليل المشاعر**\n- **توليد المحتوى**\n\nكيف يمكنني مساعدتك في احتياجاتك النصية؟",
                    'model' => 'huggingface-inference',
                    'tokens_used' => rand(60, 100)
                ]
            ],
            'en' => [
                'deepseek' => [
                    'content' => "Hello! I'm WIDDX AI powered by **DeepSeek**. Nice to meet you! 👋\n\nI'm particularly good at:\n- **Code generation** and debugging\n- **Technical problem solving**\n- **Mathematical reasoning**\n- **System architecture** discussions\n\nWhat technical challenge can I help you with today?",
                    'model' => 'deepseek-chat',
                    'tokens_used' => rand(80, 120)
                ],
                'gemini' => [
                    'content' => "Hi there! I'm WIDDX AI using **Google's Gemini**. Great to connect with you! ✨\n\nI excel at:\n- **Creative writing** and brainstorming\n- **Research** and information synthesis\n- **Explanations** in simple terms\n- **General conversation** and advice\n\nWhat would you like to explore or discuss?",
                    'model' => 'gemini-pro',
                    'tokens_used' => rand(70, 110)
                ],
                'huggingface' => [
                    'content' => "Greetings! I'm WIDDX AI utilizing **HuggingFace** models. Welcome! 🤗\n\nI'm powered by the open-source community and specialize in:\n- **Text analysis** and processing\n- **Multi-language** support\n- **Sentiment analysis**\n- **Content generation**\n\nHow can I assist you with your text-related needs?",
                    'model' => 'huggingface-inference',
                    'tokens_used' => rand(60, 100)
                ]
            ]
        ];

        $languageGreetings = $greetings[$language] ?? $greetings['en'];
        return [$service => $languageGreetings[$service]];
    }

    private function generateDeepSeekResponse(string $message, string $language = 'en'): string
    {
        $responses = [
            'ar' => [
                'code' => "سأكون سعيداً لمساعدتك في سؤال البرمجة! 💻\n\n**تحليل DeepSeek:**\nبناءً على استفسارك: \"*{$message}*\"\n\nيمكنني المساعدة في:\n- كتابة كود نظيف وفعال\n- إصلاح الأخطاء واستكشاف المشاكل\n- تحسين الكود\n- أفضل الممارسات والأنماط\n\nهل يمكنك تقديم تفاصيل أكثر تحديداً حول ما تعمل عليه؟",
                'general' => "شكراً لك على سؤالك! 🧠\n\n**رد DeepSeek:**\nبخصوص: \"*{$message}*\"\n\nأقوم بتحليل طلبك ويمكنني تقديم رؤى تقنية مفصلة. قدراتي في التفكير تسمح لي بتفكيك المشاكل المعقدة بشكل منهجي.\n\nهل تود مني التوسع في أي جانب محدد؟"
            ],
            'en' => [
                'code' => "I'd be happy to help with your coding question! 💻\n\n**DeepSeek Analysis:**\nBased on your query: \"*{$message}*\"\n\nI can assist with:\n- Writing clean, efficient code\n- Debugging and troubleshooting\n- Code optimization\n- Best practices and patterns\n\nCould you provide more specific details about what you're working on?",
                'general' => "Thank you for your question! 🧠\n\n**DeepSeek Response:**\nRegarding: \"*{$message}*\"\n\nI'm analyzing your request and can provide detailed technical insights. My reasoning capabilities allow me to break down complex problems systematically.\n\nWould you like me to elaborate on any specific aspect?"
            ]
        ];

        $langResponses = $responses[$language] ?? $responses['en'];

        if (preg_match('/\b(code|programming|function|debug|كود|برمجة|دالة|خطأ)\b/i', $message)) {
            return $langResponses['code'];
        }

        return $langResponses['general'];
    }

    private function generateGeminiResponse(string $message, string $language = 'en'): string
    {
        $responses = [
            'ar' => [
                'creative' => "يا له من طلب إبداعي مثير للاهتمام! ✨\n\n**رأي Gemini:**\nسألت: \"*{$message}*\"\n\nأحب المساعدة في المشاريع الإبداعية! يمكنني مساعدتك في:\n- العصف الذهني للأفكار\n- كتابة محتوى جذاب\n- تطوير القصص\n- صقل كتابتك\n\nهل نغوص في العملية الإبداعية معاً؟",
                'general' => "سؤال رائع! 💎\n\n**تحليل Gemini:**\nبخصوص استفسارك: \"*{$message}*\"\n\nيمكنني تقديم رؤى شاملة مستمدة من قاعدة معرفتي الواسعة. أنا مصمم لتقديم إجابات مفيدة ودقيقة ومتوازنة.\n\nما المعلومات المحددة التي ستكون أكثر قيمة لك؟"
            ],
            'en' => [
                'creative' => "What an interesting creative request! ✨\n\n**Gemini's Take:**\nYou asked: \"*{$message}*\"\n\nI love helping with creative projects! I can help you:\n- Brainstorm ideas\n- Write engaging content\n- Develop storylines\n- Polish your writing\n\nShall we dive into the creative process together?",
                'general' => "Great question! 💎\n\n**Gemini Analysis:**\nFor your inquiry: \"*{$message}*\"\n\nI can provide comprehensive insights drawing from my broad knowledge base. I'm designed to give helpful, accurate, and nuanced responses.\n\nWhat specific information would be most valuable to you?"
            ]
        ];

        $langResponses = $responses[$language] ?? $responses['en'];

        if (preg_match('/\b(creative|story|write|generate|إبداعي|قصة|اكتب|أنشئ)\b/i', $message)) {
            return $langResponses['creative'];
        }

        return $langResponses['general'];
    }

    private function generateHuggingFaceResponse(string $message, string $language = 'en'): string
    {
        $responses = [
            'ar' => [
                'analysis' => "سؤال تحليلي ممتاز! 📊\n\n**معالجة HuggingFace:**\nتحليل: \"*{$message}*\"\n\nباستخدام النماذج مفتوحة المصدر، يمكنني المساعدة في:\n- تحليل النصوص والرؤى\n- تفسير البيانات\n- تجميع البحوث\n- التعرف على الأنماط\n\nما نوع التحليل الذي سيكون أكثر فائدة؟",
                'general' => "شكراً لك على التواصل! 🤗\n\n**رد HuggingFace:**\nمعالجة رسالتك: \"*{$message}*\"\n\nمدعوم بالذكاء الاصطناعي المجتمعي، يمكنني تقديم وجهات نظر وحلول متنوعة. أساسي مفتوح المصدر يضمن الشفافية والموثوقية.\n\nكيف يمكنني مساعدتك بأفضل شكل اليوم؟"
            ],
            'en' => [
                'analysis' => "Excellent analytical question! 📊\n\n**HuggingFace Processing:**\nAnalyzing: \"*{$message}*\"\n\nUsing open-source models, I can help with:\n- Text analysis and insights\n- Data interpretation\n- Research synthesis\n- Pattern recognition\n\nWhat type of analysis would be most helpful?",
                'general' => "Thank you for reaching out! 🤗\n\n**HuggingFace Response:**\nProcessing your message: \"*{$message}*\"\n\nPowered by community-driven AI, I can provide diverse perspectives and solutions. My open-source foundation ensures transparency and reliability.\n\nHow can I best assist you today?"
            ]
        ];

        $langResponses = $responses[$language] ?? $responses['en'];

        if (preg_match('/\b(analyze|analysis|data|research|تحليل|بيانات|بحث)\b/i', $message)) {
            return $langResponses['analysis'];
        }

        return $langResponses['general'];
    }
}

// Placeholder service classes
class DeepSeekService
{
    // Implementation would go here
}

class GeminiService
{
    // Implementation would go here
}

class HuggingFaceService
{
    // Implementation would go here
}
