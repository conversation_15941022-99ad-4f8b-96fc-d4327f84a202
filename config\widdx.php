<?php

return [
    /*
    |--------------------------------------------------------------------------
    | WIDDX AI Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for WIDDX AI v2.0 system
    |
    */

    // Core System Settings
    'version' => '2.0.0',
    'name' => 'WIDDX AI',
    'description' => 'Next-Generation AI Assistant',

    // Feature Toggles
    'features' => [
        'self_learning' => env('WIDDX_SELF_LEARNING', true),
        'chain_of_thought' => env('WIDDX_CHAIN_OF_THOUGHT', true),
        'multi_modal' => env('WIDDX_MULTI_MODAL', true),
        'personality_engine' => env('WIDDX_PERSONALITY_ENGINE', true),
        'multi_agent_routing' => env('WIDDX_MULTI_AGENT_ROUTING', true),
        'internal_tools' => env('WIDDX_INTERNAL_TOOLS', true),
        'voice_input' => env('WIDDX_VOICE_INPUT', false),
        'real_time_typing' => env('WIDDX_REAL_TIME_TYPING', true),
    ],

    // Model Configuration
    'models' => [
        'deepseek' => [
            'enabled' => env('DEEPSEEK_ENABLED', true),
            'api_key' => env('DEEPSEEK_API_KEY'),
            'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
            'model' => env('DEEPSEEK_MODEL', 'deepseek-chat'),
            'max_tokens' => env('DEEPSEEK_MAX_TOKENS', 4000),
            'temperature' => env('DEEPSEEK_TEMPERATURE', 0.7),
            'specialties' => ['coding', 'analysis', 'reasoning'],
        ],
        'gemini' => [
            'enabled' => env('GEMINI_ENABLED', true),
            'api_key' => env('GEMINI_API_KEY'),
            'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
            'model' => env('GEMINI_MODEL', 'gemini-pro'),
            'max_tokens' => env('GEMINI_MAX_TOKENS', 4000),
            'temperature' => env('GEMINI_TEMPERATURE', 0.8),
            'specialties' => ['creative', 'research', 'general'],
        ],
        'huggingface' => [
            'enabled' => env('HUGGINGFACE_ENABLED', true),
            'api_key' => env('HUGGINGFACE_API_KEY'),
            'base_url' => env('HUGGINGFACE_BASE_URL', 'https://api-inference.huggingface.co'),
            'model' => env('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-large'),
            'max_tokens' => env('HUGGINGFACE_MAX_TOKENS', 2000),
            'temperature' => env('HUGGINGFACE_TEMPERATURE', 0.7),
            'specialties' => ['nlp', 'multilingual', 'analysis'],
        ],
    ],

    // Personality Engine Settings
    'personality' => [
        'default_personality' => 'widdx-default',
        'memory_retention_days' => env('WIDDX_MEMORY_RETENTION_DAYS', 365),
        'confidence_threshold' => env('WIDDX_CONFIDENCE_THRESHOLD', 0.7),
        'learning_rate' => env('WIDDX_LEARNING_RATE', 0.1),
        'max_memories_per_user' => env('WIDDX_MAX_MEMORIES_PER_USER', 10000),
    ],

    // Self-Learning Configuration
    'learning' => [
        'enabled' => env('WIDDX_LEARNING_ENABLED', true),
        'min_interaction_quality' => env('WIDDX_MIN_INTERACTION_QUALITY', 3),
        'auto_cleanup_days' => env('WIDDX_AUTO_CLEANUP_DAYS', 30),
        'max_knowledge_entries' => env('WIDDX_MAX_KNOWLEDGE_ENTRIES', 100000),
        'embedding_model' => env('WIDDX_EMBEDDING_MODEL', 'text-embedding-ada-002'),
        'similarity_threshold' => env('WIDDX_SIMILARITY_THRESHOLD', 0.8),
    ],

    // Chain of Thought Settings
    'reasoning' => [
        'enabled' => env('WIDDX_REASONING_ENABLED', true),
        'max_steps' => env('WIDDX_MAX_REASONING_STEPS', 10),
        'confidence_threshold' => env('WIDDX_REASONING_CONFIDENCE_THRESHOLD', 0.6),
        'show_steps_by_default' => env('WIDDX_SHOW_REASONING_STEPS', true),
    ],

    // Multi-Modal Support
    'multimodal' => [
        'max_file_size' => env('WIDDX_MAX_FILE_SIZE', 10240), // KB
        'allowed_types' => [
            'documents' => ['pdf', 'docx', 'txt', 'md'],
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'audio' => ['mp3', 'wav', 'ogg', 'm4a'],
            'data' => ['csv', 'xlsx', 'json', 'xml'],
        ],
        'storage_disk' => env('WIDDX_STORAGE_DISK', 'local'),
        'ocr_enabled' => env('WIDDX_OCR_ENABLED', false),
        'stt_enabled' => env('WIDDX_STT_ENABLED', false),
    ],

    // Internal Tools Configuration
    'tools' => [
        'calculator' => [
            'enabled' => true,
            'handler' => 'App\\Services\\Tools\\CalculatorTool',
            'timeout' => 30,
        ],
        'web_search' => [
            'enabled' => env('WIDDX_WEB_SEARCH_ENABLED', false),
            'handler' => 'App\\Services\\Tools\\WebSearchTool',
            'api_key' => env('SEARCH_API_KEY'),
            'timeout' => 60,
        ],
        'code_analyzer' => [
            'enabled' => true,
            'handler' => 'App\\Services\\Tools\\CodeAnalyzerTool',
            'timeout' => 45,
        ],
        'data_analyzer' => [
            'enabled' => true,
            'handler' => 'App\\Services\\Tools\\DataAnalyzerTool',
            'timeout' => 60,
        ],
        'summarizer' => [
            'enabled' => true,
            'handler' => 'App\\Services\\Tools\\SummarizerTool',
            'timeout' => 30,
        ],
    ],

    // Performance Settings
    'performance' => [
        'cache_ttl' => env('WIDDX_CACHE_TTL', 3600), // seconds
        'queue_connection' => env('WIDDX_QUEUE_CONNECTION', 'redis'),
        'max_concurrent_requests' => env('WIDDX_MAX_CONCURRENT_REQUESTS', 10),
        'response_timeout' => env('WIDDX_RESPONSE_TIMEOUT', 120), // seconds
        'rate_limit_per_minute' => env('WIDDX_RATE_LIMIT_PER_MINUTE', 60),
    ],

    // UI Configuration
    'ui' => [
        'default_theme' => env('WIDDX_DEFAULT_THEME', 'dark'),
        'supported_languages' => ['en', 'ar'],
        'default_language' => env('WIDDX_DEFAULT_LANGUAGE', 'ar'),
        'messages_per_page' => env('WIDDX_MESSAGES_PER_PAGE', 50),
        'conversations_per_page' => env('WIDDX_CONVERSATIONS_PER_PAGE', 20),
        'auto_scroll' => env('WIDDX_AUTO_SCROLL', true),
        'typing_indicator' => env('WIDDX_TYPING_INDICATOR', true),
        'sound_notifications' => env('WIDDX_SOUND_NOTIFICATIONS', false),
    ],

    // Security Settings
    'security' => [
        'encrypt_conversations' => env('WIDDX_ENCRYPT_CONVERSATIONS', true),
        'log_user_actions' => env('WIDDX_LOG_USER_ACTIONS', true),
        'session_timeout' => env('WIDDX_SESSION_TIMEOUT', 7200), // seconds
        'max_login_attempts' => env('WIDDX_MAX_LOGIN_ATTEMPTS', 5),
        'require_email_verification' => env('WIDDX_REQUIRE_EMAIL_VERIFICATION', false),
    ],

    // Monitoring & Analytics
    'monitoring' => [
        'enabled' => env('WIDDX_MONITORING_ENABLED', true),
        'track_performance' => env('WIDDX_TRACK_PERFORMANCE', true),
        'track_user_satisfaction' => env('WIDDX_TRACK_USER_SATISFACTION', true),
        'error_reporting' => env('WIDDX_ERROR_REPORTING', true),
        'metrics_retention_days' => env('WIDDX_METRICS_RETENTION_DAYS', 90),
    ],

    // Admin Panel Settings
    'admin' => [
        'enabled' => env('WIDDX_ADMIN_ENABLED', true),
        'require_2fa' => env('WIDDX_ADMIN_REQUIRE_2FA', false),
        'session_timeout' => env('WIDDX_ADMIN_SESSION_TIMEOUT', 3600),
        'allowed_ips' => env('WIDDX_ADMIN_ALLOWED_IPS', ''),
        'backup_enabled' => env('WIDDX_BACKUP_ENABLED', true),
        'backup_frequency' => env('WIDDX_BACKUP_FREQUENCY', 'daily'),
    ],

    // API Configuration
    'api' => [
        'version' => 'v2',
        'rate_limit' => env('WIDDX_API_RATE_LIMIT', 100), // per minute
        'pagination_limit' => env('WIDDX_API_PAGINATION_LIMIT', 100),
        'enable_cors' => env('WIDDX_API_ENABLE_CORS', true),
        'allowed_origins' => env('WIDDX_API_ALLOWED_ORIGINS', '*'),
        'require_auth' => env('WIDDX_API_REQUIRE_AUTH', true),
    ],

    // Development Settings
    'debug' => [
        'enabled' => env('WIDDX_DEBUG', false),
        'log_level' => env('WIDDX_LOG_LEVEL', 'info'),
        'show_reasoning_details' => env('WIDDX_SHOW_REASONING_DETAILS', false),
        'mock_responses' => env('WIDDX_MOCK_RESPONSES', false),
        'performance_profiling' => env('WIDDX_PERFORMANCE_PROFILING', false),
    ],
];
