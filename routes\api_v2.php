<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V2\{
    ConversationController,
    Personality<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON>roller,
    <PERSON>U<PERSON><PERSON><PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON>ontroller
};

/*
|--------------------------------------------------------------------------
| WIDDX AI v2 API Routes
|--------------------------------------------------------------------------
| Next-generation API routes for advanced WIDDX AI features
*/

Route::prefix('v2')->middleware(['auth:sanctum'])->group(function () {
    
    // Core Conversation API
    Route::prefix('conversations')->group(function () {
        Route::get('/', [ConversationController::class, 'index']);
        Route::post('/', [ConversationController::class, 'store']);
        Route::get('/{conversation}', [ConversationController::class, 'show']);
        Route::put('/{conversation}', [ConversationController::class, 'update']);
        Route::delete('/{conversation}', [ConversationController::class, 'destroy']);
        
        // Messages within conversations
        Route::post('/{conversation}/messages', [ConversationController::class, 'sendMessage']);
        Route::get('/{conversation}/messages', [ConversationController::class, 'getMessages']);
        
        // Advanced features
        Route::post('/{conversation}/regenerate', [ConversationController::class, 'regenerateResponse']);
        Route::post('/{conversation}/branch', [ConversationController::class, 'branchConversation']);
        Route::get('/{conversation}/reasoning', [ConversationController::class, 'getReasoningSteps']);
    });

    // Personality Management
    Route::prefix('personality')->group(function () {
        Route::get('/', [PersonalityController::class, 'getUserPersonality']);
        Route::put('/', [PersonalityController::class, 'updatePersonality']);
        Route::get('/available', [PersonalityController::class, 'getAvailablePersonalities']);
        Route::post('/switch', [PersonalityController::class, 'switchPersonality']);
        
        // Memory management
        Route::get('/memories', [PersonalityController::class, 'getMemories']);
        Route::post('/memories', [PersonalityController::class, 'storeMemory']);
        Route::delete('/memories/{type}/{key}', [PersonalityController::class, 'deleteMemory']);
    });

    // Knowledge Base
    Route::prefix('knowledge')->group(function () {
        Route::get('/search', [KnowledgeController::class, 'search']);
        Route::get('/entries', [KnowledgeController::class, 'index']);
        Route::post('/entries', [KnowledgeController::class, 'store']);
        Route::get('/entries/{entry}', [KnowledgeController::class, 'show']);
        Route::put('/entries/{entry}', [KnowledgeController::class, 'update']);
        Route::delete('/entries/{entry}', [KnowledgeController::class, 'destroy']);
        
        // Knowledge operations
        Route::post('/entries/{entry}/verify', [KnowledgeController::class, 'verify']);
        Route::get('/similar/{entry}', [KnowledgeController::class, 'findSimilar']);
        Route::post('/optimize', [KnowledgeController::class, 'optimize']);
    });

    // File Upload & Multi-Modal
    Route::prefix('files')->group(function () {
        Route::post('/upload', [FileUploadController::class, 'upload']);
        Route::get('/', [FileUploadController::class, 'index']);
        Route::get('/{file}', [FileUploadController::class, 'show']);
        Route::delete('/{file}', [FileUploadController::class, 'destroy']);
        
        // File processing
        Route::post('/{file}/process', [FileUploadController::class, 'process']);
        Route::get('/{file}/analysis', [FileUploadController::class, 'getAnalysis']);
        Route::post('/{file}/extract-text', [FileUploadController::class, 'extractText']);
    });

    // Internal Tools
    Route::prefix('tools')->group(function () {
        Route::get('/', [ToolController::class, 'index']);
        Route::post('/{tool}/execute', [ToolController::class, 'execute']);
        Route::get('/executions', [ToolController::class, 'getExecutions']);
        Route::get('/executions/{execution}', [ToolController::class, 'getExecution']);
    });

    // User Settings & Preferences
    Route::prefix('settings')->group(function () {
        Route::get('/', [ConversationController::class, 'getSettings']);
        Route::put('/', [ConversationController::class, 'updateSettings']);
        Route::post('/reset', [ConversationController::class, 'resetSettings']);
    });
});

// Admin Routes (separate middleware)
Route::prefix('v2/admin')->middleware(['auth:sanctum', 'admin'])->group(function () {
    
    // System Overview
    Route::get('/dashboard', [AdminController::class, 'dashboard']);
    Route::get('/stats', [AdminController::class, 'getStats']);
    
    // Model Performance
    Route::get('/models/performance', [AdminController::class, 'getModelPerformance']);
    Route::post('/models/performance/reset', [AdminController::class, 'resetModelPerformance']);
    
    // Knowledge Base Management
    Route::get('/knowledge/stats', [AdminController::class, 'getKnowledgeStats']);
    Route::post('/knowledge/rebuild-embeddings', [AdminController::class, 'rebuildEmbeddings']);
    Route::post('/knowledge/cleanup', [AdminController::class, 'cleanupKnowledge']);
    
    // User Management
    Route::get('/users', [AdminController::class, 'getUsers']);
    Route::get('/users/{user}/conversations', [AdminController::class, 'getUserConversations']);
    Route::get('/users/{user}/memories', [AdminController::class, 'getUserMemories']);
    
    // System Configuration
    Route::get('/config', [AdminController::class, 'getConfig']);
    Route::put('/config', [AdminController::class, 'updateConfig']);
    
    // Logs and Monitoring
    Route::get('/logs', [AdminController::class, 'getLogs']);
    Route::get('/logs/errors', [AdminController::class, 'getErrorLogs']);
    Route::post('/logs/clear', [AdminController::class, 'clearLogs']);
    
    // Tools Management
    Route::get('/tools', [AdminController::class, 'getTools']);
    Route::post('/tools', [AdminController::class, 'createTool']);
    Route::put('/tools/{tool}', [AdminController::class, 'updateTool']);
    Route::delete('/tools/{tool}', [AdminController::class, 'deleteTool']);
    Route::post('/tools/{tool}/toggle', [AdminController::class, 'toggleTool']);
    
    // Personality Management
    Route::get('/personalities', [AdminController::class, 'getPersonalities']);
    Route::post('/personalities', [AdminController::class, 'createPersonality']);
    Route::put('/personalities/{personality}', [AdminController::class, 'updatePersonality']);
    Route::delete('/personalities/{personality}', [AdminController::class, 'deletePersonality']);
});

// Public API (no auth required)
Route::prefix('v2/public')->group(function () {
    Route::get('/health', function () {
        return response()->json([
            'status' => 'healthy',
            'version' => '2.0.0',
            'timestamp' => now()->toISOString(),
            'services' => [
                'database' => 'connected',
                'cache' => 'active',
                'queue' => 'running'
            ]
        ]);
    });
    
    Route::get('/capabilities', function () {
        return response()->json([
            'features' => [
                'multi_modal_input' => true,
                'chain_of_thought' => true,
                'self_learning' => true,
                'personality_engine' => true,
                'multi_agent_routing' => true,
                'internal_tools' => true
            ],
            'supported_file_types' => [
                'documents' => ['pdf', 'docx', 'txt'],
                'images' => ['jpg', 'jpeg', 'png', 'gif'],
                'audio' => ['mp3', 'wav', 'ogg'],
                'data' => ['csv', 'xlsx', 'json']
            ],
            'languages' => ['en', 'ar'],
            'models' => ['deepseek', 'gemini', 'huggingface']
        ]);
    });
});

// WebSocket routes for real-time features
Route::prefix('v2/ws')->group(function () {
    // These would be handled by WebSocket server
    // Placeholder for documentation
    /*
    Route::get('/chat/{conversation}', 'WebSocketController@chat');
    Route::get('/typing-indicator/{conversation}', 'WebSocketController@typingIndicator');
    Route::get('/live-reasoning/{conversation}', 'WebSocketController@liveReasoning');
    */
});
