<?php

namespace App\Services\Core;

use App\Models\KnowledgeEntry;
use App\Models\Embedding;
use App\Models\Message;
use App\Services\Core\EmbeddingService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use App\Jobs\ProcessKnowledgeEntry;
use App\Jobs\GenerateEmbedding;

/**
 * Self-Learning & Autonomous Knowledge Growth Engine
 * Automatically learns from conversations and builds knowledge base
 */
class SelfLearningEngine
{
    private EmbeddingService $embeddingService;
    private float $confidenceThreshold = 0.7;
    private int $minInteractionQuality = 3; // 1-5 scale

    public function __construct(EmbeddingService $embeddingService)
    {
        $this->embeddingService = $embeddingService;
    }

    /**
     * Learn from a completed conversation
     */
    public function learnFromConversation(int $conversationId, int $userId): void
    {
        $messages = Message::where('conversation_id', $conversationId)
            ->orderBy('created_at')
            ->get();

        if ($messages->count() < 2) {
            return; // Need at least question and answer
        }

        $learningCandidates = $this->identifyLearningCandidates($messages);
        
        foreach ($learningCandidates as $candidate) {
            $this->processLearningCandidate($candidate, $userId);
        }
    }

    /**
     * Identify valuable Q&A pairs for learning
     */
    private function identifyLearningCandidates(array $messages): array
    {
        $candidates = [];
        $currentPair = null;

        foreach ($messages as $message) {
            if ($message->role === 'user') {
                $currentPair = [
                    'question' => $message,
                    'answer' => null,
                    'quality_score' => 0
                ];
            } elseif ($message->role === 'assistant' && $currentPair) {
                $currentPair['answer'] = $message;
                $currentPair['quality_score'] = $this->assessQualityScore($currentPair);
                
                if ($currentPair['quality_score'] >= $this->minInteractionQuality) {
                    $candidates[] = $currentPair;
                }
                
                $currentPair = null;
            }
        }

        return $candidates;
    }

    /**
     * Assess the quality of a Q&A pair for learning
     */
    private function assessQualityScore(array $pair): float
    {
        $score = 0;
        $question = $pair['question']->content;
        $answer = $pair['answer']->content;

        // Length and detail factors
        $questionLength = strlen($question);
        $answerLength = strlen($answer);
        
        if ($questionLength > 20 && $answerLength > 50) {
            $score += 1;
        }

        // Specificity (contains technical terms, examples)
        if (preg_match('/\b(how|what|why|explain|example|كيف|ما|لماذا|اشرح|مثال)\b/i', $question)) {
            $score += 1;
        }

        // Answer quality (structured, contains examples, code)
        if (preg_match('/```|1\.|2\.|•|-|\*/', $answer)) {
            $score += 1; // Structured answer
        }

        // Confidence from metadata
        if (isset($pair['answer']->metadata['confidence_score'])) {
            $confidence = $pair['answer']->metadata['confidence_score'];
            if ($confidence > $this->confidenceThreshold) {
                $score += 1;
            }
        }

        // Domain expertise indicators
        if ($this->containsDomainExpertise($question, $answer)) {
            $score += 1;
        }

        return min($score, 5); // Cap at 5
    }

    /**
     * Process a learning candidate and create knowledge entry
     */
    private function processLearningCandidate(array $candidate, int $userId): void
    {
        $question = $candidate['question']->content;
        $answer = $candidate['answer']->content;
        $qualityScore = $candidate['quality_score'];

        // Extract key information
        $title = $this->generateTitle($question);
        $summary = $this->generateSummary($answer);
        $tags = $this->extractTags($question, $answer);
        $category = $this->categorizeContent($question, $answer);

        // Create knowledge entry
        $knowledgeEntry = KnowledgeEntry::create([
            'title' => $title,
            'content' => $this->formatKnowledgeContent($question, $answer),
            'summary' => $summary,
            'tags' => $tags,
            'source_type' => 'user_qa',
            'source_id' => $candidate['question']->id,
            'created_by_user_id' => $userId,
            'model_used' => $candidate['answer']->metadata['model_used'] ?? 'unknown',
            'confidence_score' => $qualityScore / 5.0,
            'usage_count' => 0,
            'is_verified' => $qualityScore >= 4
        ]);

        // Queue embedding generation
        Queue::push(new GenerateEmbedding($knowledgeEntry));

        Log::info('SelfLearningEngine: Created knowledge entry', [
            'id' => $knowledgeEntry->id,
            'title' => $title,
            'quality_score' => $qualityScore,
            'category' => $category
        ]);
    }

    /**
     * Generate embeddings for knowledge retrieval
     */
    public function generateEmbeddings(KnowledgeEntry $entry): void
    {
        try {
            // Generate embedding for the full content
            $contentEmbedding = $this->embeddingService->generateEmbedding(
                $entry->title . "\n" . $entry->content
            );

            Embedding::create([
                'embeddable_type' => KnowledgeEntry::class,
                'embeddable_id' => $entry->id,
                'model_name' => $this->embeddingService->getModelName(),
                'vector' => $contentEmbedding,
                'dimensions' => count($contentEmbedding)
            ]);

            // Generate separate embedding for summary if exists
            if ($entry->summary) {
                $summaryEmbedding = $this->embeddingService->generateEmbedding($entry->summary);
                
                Embedding::create([
                    'embeddable_type' => KnowledgeEntry::class,
                    'embeddable_id' => $entry->id . '_summary',
                    'model_name' => $this->embeddingService->getModelName(),
                    'vector' => $summaryEmbedding,
                    'dimensions' => count($summaryEmbedding)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate embeddings', [
                'entry_id' => $entry->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Retrieve similar knowledge entries using embeddings
     */
    public function retrieveSimilarKnowledge(string $query, int $limit = 5): array
    {
        try {
            $queryEmbedding = $this->embeddingService->generateEmbedding($query);
            
            // This would use vector similarity search
            // For now, using simple text search as placeholder
            $entries = KnowledgeEntry::whereFullText(['title', 'content', 'summary'], $query)
                ->orderBy('confidence_score', 'desc')
                ->orderBy('usage_count', 'desc')
                ->limit($limit)
                ->get();

            // Update usage count for retrieved entries
            foreach ($entries as $entry) {
                $entry->increment('usage_count');
                $entry->update(['last_used_at' => now()]);
            }

            return $entries->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to retrieve similar knowledge', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Auto-improve knowledge base by merging similar entries
     */
    public function optimizeKnowledgeBase(): void
    {
        // Find duplicate or very similar entries
        $duplicates = $this->findDuplicateEntries();
        
        foreach ($duplicates as $group) {
            $this->mergeSimilarEntries($group);
        }

        // Remove low-quality entries that haven't been used
        $this->cleanupUnusedEntries();
    }

    // Helper methods
    private function generateTitle(string $question): string
    {
        // Extract key phrases and create concise title
        $title = trim(preg_replace('/\?.*$/', '', $question));
        return substr($title, 0, 100);
    }

    private function generateSummary(string $answer): string
    {
        // Extract first meaningful sentence or paragraph
        $sentences = preg_split('/[.!?]+/', $answer);
        $summary = '';
        
        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (strlen($sentence) > 20) {
                $summary = $sentence;
                break;
            }
        }
        
        return substr($summary, 0, 200);
    }

    private function extractTags(string $question, string $answer): array
    {
        $tags = [];
        $text = strtolower($question . ' ' . $answer);
        
        // Programming languages
        $programmingTags = ['php', 'javascript', 'python', 'java', 'css', 'html', 'sql'];
        foreach ($programmingTags as $tag) {
            if (strpos($text, $tag) !== false) {
                $tags[] = $tag;
            }
        }

        // General categories
        $categoryTags = ['coding', 'design', 'database', 'api', 'frontend', 'backend'];
        foreach ($categoryTags as $tag) {
            if (strpos($text, $tag) !== false) {
                $tags[] = $tag;
            }
        }

        return array_unique($tags);
    }

    private function categorizeContent(string $question, string $answer): string
    {
        $text = strtolower($question . ' ' . $answer);
        
        if (preg_match('/\b(code|programming|function|class|method)\b/', $text)) {
            return 'programming';
        } elseif (preg_match('/\b(design|ui|ux|interface)\b/', $text)) {
            return 'design';
        } elseif (preg_match('/\b(database|sql|query|table)\b/', $text)) {
            return 'database';
        } elseif (preg_match('/\b(api|rest|endpoint|request)\b/', $text)) {
            return 'api';
        }
        
        return 'general';
    }

    private function formatKnowledgeContent(string $question, string $answer): string
    {
        return "**Question:** {$question}\n\n**Answer:** {$answer}";
    }

    private function containsDomainExpertise(string $question, string $answer): bool
    {
        // Check for technical depth, code examples, specific methodologies
        return preg_match('/```|class |function |def |import |SELECT |CREATE |UPDATE/', $answer) ||
               str_word_count($answer) > 100;
    }

    private function findDuplicateEntries(): array
    {
        // Simplified duplicate detection - would use embeddings in production
        return [];
    }

    private function mergeSimilarEntries(array $entries): void
    {
        // Merge logic for similar entries
    }

    private function cleanupUnusedEntries(): void
    {
        KnowledgeEntry::where('usage_count', 0)
            ->where('confidence_score', '<', 0.5)
            ->where('created_at', '<', now()->subDays(30))
            ->delete();
    }
}
